package hyperliquid

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	"github.com/yudaprama/hyperliquid-go-sdk/utils"
)

// ActiveSubscription represents an active subscription with callback and ID
type ActiveSubscription struct {
	Callback       func(utils.Any)
	SubscriptionID int
}

// WebsocketManager manages websocket connections and subscriptions
type WebsocketManager struct {
	subscriptionIDCounter int
	wsReady               bool
	queuedSubscriptions   []QueuedSubscription
	activeSubscriptions   map[string][]ActiveSubscription
	conn                  *websocket.Conn
	logger                *logrus.Logger
	baseURL               string
	ctx                   context.Context
	cancel                context.CancelFunc
	mu                    sync.RWMutex
	stopCh                chan struct{}
	pingTicker            *time.Ticker
}

// QueuedSubscription represents a subscription waiting to be processed
type QueuedSubscription struct {
	Subscription utils.Subscription
	Active       ActiveSubscription
}

// NewWebsocketManager creates a new websocket manager
func NewWebsocketManager(baseURL string) *WebsocketManager {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &WebsocketManager{
		subscriptionIDCounter: 0,
		wsReady:               false,
		queuedSubscriptions:   make([]QueuedSubscription, 0),
		activeSubscriptions:   make(map[string][]ActiveSubscription),
		logger:                logrus.New(),
		baseURL:               baseURL,
		ctx:                   ctx,
		cancel:                cancel,
		stopCh:                make(chan struct{}),
	}
}

// Start starts the websocket manager
func (wm *WebsocketManager) Start() error {
	go wm.run()
	return nil
}

// Stop stops the websocket manager
func (wm *WebsocketManager) Stop() {
	wm.cancel()
	close(wm.stopCh)
	
	if wm.pingTicker != nil {
		wm.pingTicker.Stop()
	}
	
	if wm.conn != nil {
		wm.conn.Close()
	}
}

// run is the main goroutine for the websocket manager
func (wm *WebsocketManager) run() {
	// Start ping sender
	go wm.sendPing()
	
	// Connect to websocket
	wsURL := strings.Replace(wm.baseURL, "http", "ws", 1) + "/ws"
	u, err := url.Parse(wsURL)
	if err != nil {
		wm.logger.Errorf("Failed to parse websocket URL: %v", err)
		return
	}
	
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		wm.logger.Errorf("Failed to connect to websocket: %v", err)
		return
	}
	
	wm.conn = conn
	wm.onOpen()
	
	// Start message reading loop
	for {
		select {
		case <-wm.ctx.Done():
			return
		default:
			_, message, err := conn.ReadMessage()
			if err != nil {
				wm.logger.Errorf("Failed to read websocket message: %v", err)
				return
			}
			wm.onMessage(message)
		}
	}
}

// sendPing sends periodic ping messages
func (wm *WebsocketManager) sendPing() {
	wm.pingTicker = time.NewTicker(50 * time.Second)
	defer wm.pingTicker.Stop()
	
	for {
		select {
		case <-wm.ctx.Done():
			wm.logger.Debug("Websocket ping sender stopped")
			return
		case <-wm.pingTicker.C:
			if wm.conn != nil {
				wm.logger.Debug("Websocket sending ping")
				pingMsg := map[string]string{"method": "ping"}
				if err := wm.conn.WriteJSON(pingMsg); err != nil {
					wm.logger.Errorf("Failed to send ping: %v", err)
				}
			}
		}
	}
}

// onOpen handles websocket connection open
func (wm *WebsocketManager) onOpen() {
	wm.logger.Debug("Websocket connection opened")
	wm.mu.Lock()
	wm.wsReady = true
	
	// Process queued subscriptions
	for _, queued := range wm.queuedSubscriptions {
		wm.subscribeInternal(queued.Subscription, queued.Active.Callback, &queued.Active.SubscriptionID)
	}
	wm.queuedSubscriptions = nil
	wm.mu.Unlock()
}

// onMessage handles incoming websocket messages
func (wm *WebsocketManager) onMessage(message []byte) {
	messageStr := string(message)
	
	if messageStr == "Websocket connection established." {
		wm.logger.Debug(messageStr)
		return
	}
	
	wm.logger.Debugf("on_message %s", messageStr)
	
	// Parse the message
	var wsMsg map[string]any
	if err := json.Unmarshal(message, &wsMsg); err != nil {
		wm.logger.Errorf("Failed to parse websocket message: %v", err)
		return
	}
	
	identifier := wm.wsMsgToIdentifier(wsMsg)
	if identifier == "pong" {
		wm.logger.Debug("Websocket received pong")
		return
	}
	
	if identifier == "" {
		wm.logger.Debug("Websocket not handling empty message")
		return
	}
	
	wm.mu.RLock()
	activeSubscriptions := wm.activeSubscriptions[identifier]
	wm.mu.RUnlock()
	
	if len(activeSubscriptions) == 0 {
		wm.logger.Warnf("Websocket message from an unexpected subscription: %s %s", messageStr, identifier)
	} else {
		for _, activeSub := range activeSubscriptions {
			activeSub.Callback(wsMsg)
		}
	}
}

// Subscribe subscribes to a websocket channel
func (wm *WebsocketManager) Subscribe(subscription utils.Subscription, callback func(utils.Any)) int {
	return wm.subscribeInternal(subscription, callback, nil)
}

// subscribeInternal handles the internal subscription logic
func (wm *WebsocketManager) subscribeInternal(subscription utils.Subscription, callback func(utils.Any), subscriptionID *int) int {
	wm.mu.Lock()
	defer wm.mu.Unlock()
	
	if subscriptionID == nil {
		wm.subscriptionIDCounter++
		id := wm.subscriptionIDCounter
		subscriptionID = &id
	}
	
	if !wm.wsReady {
		wm.logger.Debug("enqueueing subscription")
		wm.queuedSubscriptions = append(wm.queuedSubscriptions, QueuedSubscription{
			Subscription: subscription,
			Active:       ActiveSubscription{Callback: callback, SubscriptionID: *subscriptionID},
		})
	} else {
		wm.logger.Debug("subscribing")
		identifier := wm.subscriptionToIdentifier(subscription)
		
		if identifier == "userEvents" || identifier == "orderUpdates" {
			// Check if already subscribed
			if len(wm.activeSubscriptions[identifier]) != 0 {
				wm.logger.Errorf("Cannot subscribe to %s multiple times", identifier)
				return -1
			}
		}
		
		wm.activeSubscriptions[identifier] = append(wm.activeSubscriptions[identifier], 
			ActiveSubscription{Callback: callback, SubscriptionID: *subscriptionID})
		
		// Send subscription message
		subMsg := map[string]any{
			"method":       "subscribe",
			"subscription": subscription,
		}
		
		if err := wm.conn.WriteJSON(subMsg); err != nil {
			wm.logger.Errorf("Failed to send subscription: %v", err)
		}
	}
	
	return *subscriptionID
}

// Unsubscribe unsubscribes from a websocket channel
func (wm *WebsocketManager) Unsubscribe(subscription utils.Subscription, subscriptionID int) bool {
	wm.mu.Lock()
	defer wm.mu.Unlock()
	
	if !wm.wsReady {
		wm.logger.Error("Can't unsubscribe before websocket connected")
		return false
	}
	
	identifier := wm.subscriptionToIdentifier(subscription)
	activeSubscriptions := wm.activeSubscriptions[identifier]
	
	// Filter out the subscription with matching ID
	var newActiveSubscriptions []ActiveSubscription
	for _, sub := range activeSubscriptions {
		if sub.SubscriptionID != subscriptionID {
			newActiveSubscriptions = append(newActiveSubscriptions, sub)
		}
	}
	
	// If no more subscriptions for this identifier, send unsubscribe message
	if len(newActiveSubscriptions) == 0 {
		unsubMsg := map[string]any{
			"method":       "unsubscribe",
			"subscription": subscription,
		}
		
		if err := wm.conn.WriteJSON(unsubMsg); err != nil {
			wm.logger.Errorf("Failed to send unsubscription: %v", err)
		}
	}
	
	wm.activeSubscriptions[identifier] = newActiveSubscriptions
	return len(activeSubscriptions) != len(newActiveSubscriptions)
}

// subscriptionToIdentifier converts a subscription to its identifier string
func (wm *WebsocketManager) subscriptionToIdentifier(subscription utils.Subscription) string {
	switch sub := subscription.(type) {
	case utils.AllMidsSubscription:
		return "allMids"
	case utils.L2BookSubscription:
		return fmt.Sprintf("l2Book:%s", strings.ToLower(sub.Coin))
	case utils.TradesSubscription:
		return fmt.Sprintf("trades:%s", strings.ToLower(sub.Coin))
	case utils.UserEventsSubscription:
		return "userEvents"
	case utils.UserFillsSubscription:
		return fmt.Sprintf("userFills:%s", strings.ToLower(sub.User))
	case utils.CandleSubscription:
		return fmt.Sprintf("candle:%s,%s", strings.ToLower(sub.Coin), sub.Interval)
	case utils.OrderUpdatesSubscription:
		return "orderUpdates"
	case utils.UserFundingsSubscription:
		return fmt.Sprintf("userFundings:%s", strings.ToLower(sub.User))
	case utils.UserNonFundingLedgerUpdatesSubscription:
		return fmt.Sprintf("userNonFundingLedgerUpdates:%s", strings.ToLower(sub.User))
	case utils.WebData2Subscription:
		return fmt.Sprintf("webData2:%s", strings.ToLower(sub.User))
	case utils.BboSubscription:
		return fmt.Sprintf("bbo:%s", strings.ToLower(sub.Coin))
	case utils.ActiveAssetCtxSubscription:
		return fmt.Sprintf("activeAssetCtx:%s", strings.ToLower(sub.Coin))
	case utils.ActiveAssetDataSubscription:
		return fmt.Sprintf("activeAssetData:%s,%s", strings.ToLower(sub.Coin), strings.ToLower(sub.User))
	default:
		return ""
	}
}

// wsMsgToIdentifier converts a websocket message to its identifier string
func (wm *WebsocketManager) wsMsgToIdentifier(wsMsg map[string]any) string {
	channel, ok := wsMsg["channel"].(string)
	if !ok {
		return ""
	}

	switch channel {
	case "pong":
		return "pong"
	case "allMids":
		return "allMids"
	case "l2Book":
		if data, ok := wsMsg["data"].(map[string]any); ok {
			if coin, ok := data["coin"].(string); ok {
				return fmt.Sprintf("l2Book:%s", strings.ToLower(coin))
			}
		}
	case "trades":
		if data, ok := wsMsg["data"].([]any); ok && len(data) > 0 {
			if trade, ok := data[0].(map[string]any); ok {
				if coin, ok := trade["coin"].(string); ok {
					return fmt.Sprintf("trades:%s", strings.ToLower(coin))
				}
			}
		}
		return ""
	case "user":
		return "userEvents"
	case "userFills":
		if data, ok := wsMsg["data"].(map[string]any); ok {
			if user, ok := data["user"].(string); ok {
				return fmt.Sprintf("userFills:%s", strings.ToLower(user))
			}
		}
	case "candle":
		if data, ok := wsMsg["data"].(map[string]any); ok {
			if s, ok := data["s"].(string); ok {
				if i, ok := data["i"].(string); ok {
					return fmt.Sprintf("candle:%s,%s", strings.ToLower(s), i)
				}
			}
		}
	case "orderUpdates":
		return "orderUpdates"
	case "userFundings":
		if data, ok := wsMsg["data"].(map[string]any); ok {
			if user, ok := data["user"].(string); ok {
				return fmt.Sprintf("userFundings:%s", strings.ToLower(user))
			}
		}
	case "userNonFundingLedgerUpdates":
		if data, ok := wsMsg["data"].(map[string]any); ok {
			if user, ok := data["user"].(string); ok {
				return fmt.Sprintf("userNonFundingLedgerUpdates:%s", strings.ToLower(user))
			}
		}
	case "webData2":
		if data, ok := wsMsg["data"].(map[string]any); ok {
			if user, ok := data["user"].(string); ok {
				return fmt.Sprintf("webData2:%s", strings.ToLower(user))
			}
		}
	case "bbo":
		if data, ok := wsMsg["data"].(map[string]any); ok {
			if coin, ok := data["coin"].(string); ok {
				return fmt.Sprintf("bbo:%s", strings.ToLower(coin))
			}
		}
	case "activeAssetCtx", "activeSpotAssetCtx":
		if data, ok := wsMsg["data"].(map[string]any); ok {
			if coin, ok := data["coin"].(string); ok {
				return fmt.Sprintf("activeAssetCtx:%s", strings.ToLower(coin))
			}
		}
	case "activeAssetData":
		if data, ok := wsMsg["data"].(map[string]any); ok {
			if coin, ok := data["coin"].(string); ok {
				if user, ok := data["user"].(string); ok {
					return fmt.Sprintf("activeAssetData:%s,%s", strings.ToLower(coin), strings.ToLower(user))
				}
			}
		}
	}

	return ""
}
