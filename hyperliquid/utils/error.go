package utils

import (
	"fmt"
	"net/http"
)

// Error is the base error type
type Error struct {
	message string
}

func (e *Error) Error() string {
	return e.message
}

// ClientError represents a client-side error (4xx status codes)
type ClientError struct {
	StatusCode   int
	ErrorCode    *string
	ErrorMessage string
	Header       http.Header
	ErrorData    interface{}
}

func (e *ClientError) Error() string {
	if e.ErrorCode != nil {
		return fmt.Sprintf("Client error %d: %s - %s", e.StatusCode, *e.ErrorCode, e.ErrorMessage)
	}
	return fmt.Sprintf("Client error %d: %s", e.StatusCode, e.ErrorMessage)
}

// NewClientError creates a new ClientError
func NewClientError(statusCode int, errorCode *string, errorMessage string, header http.Header, errorData interface{}) *ClientError {
	return &ClientError{
		StatusCode:   statusCode,
		ErrorCode:    errorCode,
		ErrorMessage: errorMessage,
		Header:       header,
		ErrorData:    errorData,
	}
}

// ServerError represents a server-side error (5xx status codes)
type ServerError struct {
	StatusCode int
	Message    string
}

func (e *ServerError) Error() string {
	return fmt.Sprintf("Server error %d: %s", e.StatusCode, e.Message)
}

// NewServerError creates a new ServerError
func NewServerError(statusCode int, message string) *ServerError {
	return &ServerError{
		StatusCode: statusCode,
		Message:    message,
	}
}
