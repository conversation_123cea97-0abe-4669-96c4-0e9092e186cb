package utils

import (
	"crypto/ecdsa"
	"encoding/hex"
	"fmt"
	"math"
	"math/big"
	"strconv"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/vmihailenco/msgpack/v5"
)

// LocalAccount represents a wallet/account for signing
type LocalAccount struct {
	Address    string
	PrivateKey *ecdsa.PrivateKey
	privateKeyHex string
}

// NewLocalAccount creates a new LocalAccount
func NewLocalAccount(address, privateKeyHex string) *LocalAccount {
	// Remove 0x prefix if present
	if strings.HasPrefix(privateKeyHex, "0x") {
		privateKeyHex = privateKeyHex[2:]
	}

	// Convert hex string to private key
	privateKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		panic(fmt.Sprintf("Invalid private key: %v", err))
	}

	// Derive address from private key if not provided
	derivedAddress := crypto.PubkeyToAddress(privateKey.PublicKey).Hex()
	if address == "" {
		address = derivedAddress
	}

	return &LocalAccount{
		Address:       address,
		PrivateKey:    privateKey,
		privateKeyHex: privateKeyHex,
	}
}

// EIP-712 signing type definitions
type EIP712Type struct {
	Name string `json:"name"`
	Type string `json:"type"`
}

type EIP712Domain struct {
	Name              string `json:"name"`
	Version           string `json:"version"`
	ChainId           int    `json:"chainId"`
	VerifyingContract string `json:"verifyingContract"`
}

type EIP712Message struct {
	Domain      EIP712Domain           `json:"domain"`
	Types       map[string][]EIP712Type `json:"types"`
	PrimaryType string                 `json:"primaryType"`
	Message     map[string]interface{} `json:"message"`
}

// Signing type constants
var (
	UsdSendSignTypes = []EIP712Type{
		{Name: "hyperliquidChain", Type: "string"},
		{Name: "destination", Type: "string"},
		{Name: "amount", Type: "string"},
		{Name: "time", Type: "uint64"},
	}

	SpotTransferSignTypes = []EIP712Type{
		{Name: "hyperliquidChain", Type: "string"},
		{Name: "destination", Type: "string"},
		{Name: "token", Type: "string"},
		{Name: "amount", Type: "string"},
		{Name: "time", Type: "uint64"},
	}

	WithdrawSignTypes = []EIP712Type{
		{Name: "hyperliquidChain", Type: "string"},
		{Name: "destination", Type: "string"},
		{Name: "amount", Type: "string"},
		{Name: "time", Type: "uint64"},
	}

	UsdClassTransferSignTypes = []EIP712Type{
		{Name: "hyperliquidChain", Type: "string"},
		{Name: "amount", Type: "string"},
		{Name: "toPerp", Type: "bool"},
		{Name: "nonce", Type: "uint64"},
	}

	SendAssetSignTypes = []EIP712Type{
		{Name: "hyperliquidChain", Type: "string"},
		{Name: "destination", Type: "string"},
		{Name: "sourceDex", Type: "string"},
		{Name: "destinationDex", Type: "string"},
		{Name: "token", Type: "string"},
		{Name: "amount", Type: "string"},
		{Name: "fromSubAccount", Type: "string"},
		{Name: "nonce", Type: "uint64"},
	}

	TokenDelegateTypes = []EIP712Type{
		{Name: "hyperliquidChain", Type: "string"},
		{Name: "validator", Type: "address"},
		{Name: "wei", Type: "uint64"},
		{Name: "isUndelegate", Type: "bool"},
		{Name: "nonce", Type: "uint64"},
	}

	ConvertToMultiSigUserSignTypes = []EIP712Type{
		{Name: "hyperliquidChain", Type: "string"},
		{Name: "signers", Type: "string"},
		{Name: "nonce", Type: "uint64"},
	}

	MultiSigEnvelopeSignTypes = []EIP712Type{
		{Name: "hyperliquidChain", Type: "string"},
		{Name: "multiSigActionHash", Type: "bytes32"},
		{Name: "nonce", Type: "uint64"},
	}

	AgentSignTypes = []EIP712Type{
		{Name: "hyperliquidChain", Type: "string"},
		{Name: "agentAddress", Type: "address"},
		{Name: "agentName", Type: "string"},
		{Name: "nonce", Type: "uint64"},
	}

	ApproveBuilderFeeSignTypes = []EIP712Type{
		{Name: "hyperliquidChain", Type: "string"},
		{Name: "maxFeeRate", Type: "string"},
		{Name: "builder", Type: "address"},
		{Name: "nonce", Type: "uint64"},
	}

	EIP712DomainType = []EIP712Type{
		{Name: "name", Type: "string"},
		{Name: "version", Type: "string"},
		{Name: "chainId", Type: "uint256"},
		{Name: "verifyingContract", Type: "address"},
	}

	AgentType = []EIP712Type{
		{Name: "source", Type: "string"},
		{Name: "connectionId", Type: "bytes32"},
	}
)

// Helper functions
func addressToBytes(address string) []byte {
	if strings.HasPrefix(address, "0x") {
		address = address[2:]
	}
	bytes, err := hex.DecodeString(address)
	if err != nil {
		panic(fmt.Sprintf("Invalid address: %v", err))
	}
	return bytes
}

func actionHash(action map[string]interface{}, vaultAddress *string, nonce int64, expiresAfter *int64) []byte {
	// Pack the action using msgpack
	data, err := msgpack.Marshal(action)
	if err != nil {
		panic(fmt.Sprintf("Failed to marshal action: %v", err))
	}

	// Add nonce (8 bytes, big endian)
	nonceBytes := make([]byte, 8)
	for i := 7; i >= 0; i-- {
		nonceBytes[i] = byte(nonce & 0xff)
		nonce >>= 8
	}
	data = append(data, nonceBytes...)

	// Add vault address
	if vaultAddress == nil {
		data = append(data, 0x00)
	} else {
		data = append(data, 0x01)
		data = append(data, addressToBytes(*vaultAddress)...)
	}

	// Add expires after if present
	if expiresAfter != nil {
		data = append(data, 0x00)
		expiresAfterBytes := make([]byte, 8)
		expires := *expiresAfter
		for i := 7; i >= 0; i-- {
			expiresAfterBytes[i] = byte(expires & 0xff)
			expires >>= 8
		}
		data = append(data, expiresAfterBytes...)
	}

	return crypto.Keccak256(data)
}

func constructPhantomAgent(hash []byte, isMainnet bool) map[string]interface{} {
	source := "b"
	if isMainnet {
		source = "a"
	}
	return map[string]interface{}{
		"source":       source,
		"connectionId": fmt.Sprintf("0x%x", hash),
	}
}

func l1Payload(phantomAgent map[string]interface{}) EIP712Message {
	return EIP712Message{
		Domain: EIP712Domain{
			ChainId:           1337,
			Name:              "Exchange",
			VerifyingContract: "******************************************",
			Version:           "1",
		},
		Types: map[string][]EIP712Type{
			"Agent":        AgentType,
			"EIP712Domain": EIP712DomainType,
		},
		PrimaryType: "Agent",
		Message:     phantomAgent,
	}
}

func userSignedPayload(primaryType string, payloadTypes []EIP712Type, action map[string]interface{}) EIP712Message {
	chainIdStr, ok := action["signatureChainId"].(string)
	if !ok {
		panic("signatureChainId not found in action")
	}

	// Parse hex chain ID
	chainId := int64(0)
	if strings.HasPrefix(chainIdStr, "0x") {
		chainIdInt, err := strconv.ParseInt(chainIdStr[2:], 16, 64)
		if err != nil {
			panic(fmt.Sprintf("Invalid chainId: %v", err))
		}
		chainId = chainIdInt
	}

	return EIP712Message{
		Domain: EIP712Domain{
			Name:              "HyperliquidSignTransaction",
			Version:           "1",
			ChainId:           int(chainId),
			VerifyingContract: "******************************************",
		},
		Types: map[string][]EIP712Type{
			primaryType:    payloadTypes,
			"EIP712Domain": EIP712DomainType,
		},
		PrimaryType: primaryType,
		Message:     action,
	}
}

// signInner performs the actual EIP-712 signing
func signInner(wallet *LocalAccount, data EIP712Message) (*Signature, error) {
	// Create the EIP-712 hash
	hash, err := createEIP712Hash(data)
	if err != nil {
		return nil, fmt.Errorf("failed to create EIP-712 hash: %w", err)
	}

	// Sign the hash
	signature, err := crypto.Sign(hash, wallet.PrivateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to sign hash: %w", err)
	}

	// Extract r, s, v from signature
	r := signature[:32]
	s := signature[32:64]
	v := signature[64] + 27 // Add 27 for Ethereum compatibility

	return &Signature{
		R: fmt.Sprintf("0x%x", r),
		S: fmt.Sprintf("0x%x", s),
		V: int(v),
	}, nil
}

// createEIP712Hash creates the EIP-712 hash for signing
func createEIP712Hash(data EIP712Message) ([]byte, error) {
	// This is a simplified implementation of EIP-712 hashing
	// In a production environment, you would use a proper EIP-712 library

	// Create domain separator
	domainTypeHash := crypto.Keccak256([]byte("EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)"))
	nameHash := crypto.Keccak256([]byte(data.Domain.Name))
	versionHash := crypto.Keccak256([]byte(data.Domain.Version))
	chainIdBytes := make([]byte, 32)
	chainIdBig := big.NewInt(int64(data.Domain.ChainId))
	chainIdBig.FillBytes(chainIdBytes)
	contractBytes := common.HexToAddress(data.Domain.VerifyingContract).Bytes()
	contractHash := make([]byte, 32)
	copy(contractHash[12:], contractBytes)

	domainSeparator := crypto.Keccak256(append(append(append(append(domainTypeHash, nameHash...), versionHash...), chainIdBytes...), contractHash...))

	// Create struct hash (simplified - in production, use proper encoding)
	messageBytes, err := msgpack.Marshal(data.Message)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal message: %w", err)
	}
	structHash := crypto.Keccak256(messageBytes)

	// Create final hash
	finalHash := crypto.Keccak256(append(append([]byte("\x19\x01"), domainSeparator...), structHash...))

	return finalHash, nil
}

// GetTimestampMs returns current timestamp in milliseconds
func GetTimestampMs() int64 {
	return time.Now().UnixMilli()
}

// FloatToWire converts a float to wire format string
func FloatToWire(x float64) (string, error) {
	rounded := fmt.Sprintf("%.8f", x)
	parsedFloat, err := strconv.ParseFloat(rounded, 64)
	if err != nil {
		return "", err
	}
	if math.Abs(parsedFloat-x) >= 1e-12 {
		return "", fmt.Errorf("float_to_wire causes rounding: %f", x)
	}
	if rounded == "-0" {
		rounded = "0"
	}
	// Remove trailing zeros
	if parsedFloat == 0 {
		return "0", nil
	}
	return fmt.Sprintf("%g", parsedFloat), nil
}

// FloatToUsdInt converts float to USD integer (6 decimals)
func FloatToUsdInt(x float64) (int, error) {
	return FloatToInt(x, 6)
}

// FloatToInt converts float to integer with specified decimal places
func FloatToInt(x float64, power int) (int, error) {
	withDecimals := x * math.Pow10(power)
	rounded := math.Round(withDecimals)
	if math.Abs(rounded-withDecimals) >= 1e-3 {
		return 0, fmt.Errorf("float_to_int causes rounding: %f", x)
	}
	return int(rounded), nil
}

// OrderTypeToWire converts OrderType to OrderTypeWire
func OrderTypeToWire(orderType OrderType) (OrderTypeWire, error) {
	if orderType.Limit != nil {
		return OrderTypeWire{Limit: orderType.Limit}, nil
	} else if orderType.Trigger != nil {
		triggerPxStr, err := FloatToWire(orderType.Trigger.TriggerPx)
		if err != nil {
			return OrderTypeWire{}, err
		}
		return OrderTypeWire{
			Trigger: &TriggerOrderTypeWire{
				IsMarket:  orderType.Trigger.IsMarket,
				TriggerPx: triggerPxStr,
				Tpsl:      orderType.Trigger.Tpsl,
			},
		}, nil
	}
	return OrderTypeWire{}, fmt.Errorf("invalid order type")
}

// OrderRequestToOrderWire converts OrderRequest to OrderWire
func OrderRequestToOrderWire(order OrderRequest, asset int) (OrderWire, error) {
	priceStr, err := FloatToWire(order.LimitPx)
	if err != nil {
		return OrderWire{}, err
	}
	
	sizeStr, err := FloatToWire(order.Sz)
	if err != nil {
		return OrderWire{}, err
	}
	
	orderTypeWire, err := OrderTypeToWire(order.OrderType)
	if err != nil {
		return OrderWire{}, err
	}
	
	orderWire := OrderWire{
		A: asset,
		B: order.IsBuy,
		P: priceStr,
		S: sizeStr,
		R: order.ReduceOnly,
		T: orderTypeWire,
	}
	
	if order.Cloid != nil {
		cloidStr := order.Cloid.ToRaw()
		orderWire.C = &cloidStr
	}
	
	return orderWire, nil
}

// OrderWiresToOrderAction converts order wires to order action
func OrderWiresToOrderAction(orderWires []OrderWire, builder *BuilderInfo) map[string]interface{} {
	action := map[string]interface{}{
		"type":     "order",
		"orders":   orderWires,
		"grouping": "na",
	}
	if builder != nil {
		action["builder"] = builder
	}
	return action
}

// Signature represents a cryptographic signature
type Signature struct {
	R string `json:"r"`
	S string `json:"s"`
	V int    `json:"v"`
}

// SignL1Action signs an L1 action
func SignL1Action(wallet *LocalAccount, action map[string]interface{}, activePool *string, nonce int64, expiresAfter *int64, isMainnet bool) (*Signature, error) {
	hash := actionHash(action, activePool, nonce, expiresAfter)
	phantomAgent := constructPhantomAgent(hash, isMainnet)
	data := l1Payload(phantomAgent)
	return signInner(wallet, data)
}

// signUserSignedAction signs a user signed action
func signUserSignedAction(wallet *LocalAccount, action map[string]interface{}, payloadTypes []EIP712Type, primaryType string, isMainnet bool) (*Signature, error) {
	// Add signature chain ID and hyperliquid chain
	action["signatureChainId"] = "0x66eee"
	if isMainnet {
		action["hyperliquidChain"] = "Mainnet"
	} else {
		action["hyperliquidChain"] = "Testnet"
	}

	data := userSignedPayload(primaryType, payloadTypes, action)
	return signInner(wallet, data)
}

// SignUsdTransferAction signs a USD transfer action
func SignUsdTransferAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	return signUserSignedAction(wallet, action, UsdSendSignTypes, "HyperliquidTransaction:UsdSend", isMainnet)
}

// SignSpotTransferAction signs a spot transfer action
func SignSpotTransferAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	return signUserSignedAction(wallet, action, SpotTransferSignTypes, "HyperliquidTransaction:SpotSend", isMainnet)
}

// SignWithdrawFromBridgeAction signs a withdraw from bridge action
func SignWithdrawFromBridgeAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	return signUserSignedAction(wallet, action, WithdrawSignTypes, "HyperliquidTransaction:Withdraw", isMainnet)
}

// SignUsdClassTransferAction signs a USD class transfer action
func SignUsdClassTransferAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	return signUserSignedAction(wallet, action, UsdClassTransferSignTypes, "HyperliquidTransaction:UsdClassTransfer", isMainnet)
}

// SignSendAssetAction signs a send asset action
func SignSendAssetAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	return signUserSignedAction(wallet, action, SendAssetSignTypes, "HyperliquidTransaction:SendAsset", isMainnet)
}

// SignTokenDelegateAction signs a token delegate action
func SignTokenDelegateAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	return signUserSignedAction(wallet, action, TokenDelegateTypes, "HyperliquidTransaction:TokenDelegate", isMainnet)
}

// SignAgent signs an agent action
func SignAgent(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	return signUserSignedAction(wallet, action, AgentSignTypes, "HyperliquidTransaction:ApproveAgent", isMainnet)
}

// SignApproveBuilderFee signs an approve builder fee action
func SignApproveBuilderFee(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	return signUserSignedAction(wallet, action, ApproveBuilderFeeSignTypes, "HyperliquidTransaction:ApproveBuilderFee", isMainnet)
}

// SignConvertToMultiSigUserAction signs a convert to multi sig user action
func SignConvertToMultiSigUserAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	return signUserSignedAction(wallet, action, ConvertToMultiSigUserSignTypes, "HyperliquidTransaction:ConvertToMultiSigUser", isMainnet)
}

// SignMultiSigAction signs a multi sig action
func SignMultiSigAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool, vaultAddress *string, nonce int64, expiresAfter *int64) (*Signature, error) {
	actionWithoutTag := make(map[string]interface{})
	for k, v := range action {
		if k != "type" {
			actionWithoutTag[k] = v
		}
	}

	multiSigActionHash := actionHash(actionWithoutTag, vaultAddress, nonce, expiresAfter)
	envelope := map[string]interface{}{
		"multiSigActionHash": fmt.Sprintf("0x%x", multiSigActionHash),
		"nonce":              nonce,
	}

	return signUserSignedAction(wallet, envelope, MultiSigEnvelopeSignTypes, "HyperliquidTransaction:SendMultiSig", isMainnet)
}


