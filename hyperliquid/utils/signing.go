package utils

import (
	"fmt"
	"math"
	"strconv"
	"time"
)

// LocalAccount represents a wallet/account for signing
type LocalAccount struct {
	Address    string
	PrivateKey string
}

// NewLocalAccount creates a new LocalAccount
func NewLocalAccount(address, privateKey string) *LocalAccount {
	return &LocalAccount{
		Address:    address,
		PrivateKey: privateKey,
	}
}

// GetTimestampMs returns current timestamp in milliseconds
func GetTimestampMs() int64 {
	return time.Now().UnixMilli()
}

// FloatToWire converts a float to wire format string
func FloatToWire(x float64) (string, error) {
	rounded := fmt.Sprintf("%.8f", x)
	parsedFloat, err := strconv.ParseFloat(rounded, 64)
	if err != nil {
		return "", err
	}
	if math.Abs(parsedFloat-x) >= 1e-12 {
		return "", fmt.Errorf("float_to_wire causes rounding: %f", x)
	}
	if rounded == "-0" {
		rounded = "0"
	}
	// Remove trailing zeros
	if parsedFloat == 0 {
		return "0", nil
	}
	return fmt.Sprintf("%g", parsedFloat), nil
}

// FloatToUsdInt converts float to USD integer (6 decimals)
func FloatToUsdInt(x float64) (int, error) {
	return FloatToInt(x, 6)
}

// FloatToInt converts float to integer with specified decimal places
func FloatToInt(x float64, power int) (int, error) {
	withDecimals := x * math.Pow10(power)
	rounded := math.Round(withDecimals)
	if math.Abs(rounded-withDecimals) >= 1e-3 {
		return 0, fmt.Errorf("float_to_int causes rounding: %f", x)
	}
	return int(rounded), nil
}

// OrderTypeToWire converts OrderType to OrderTypeWire
func OrderTypeToWire(orderType OrderType) (OrderTypeWire, error) {
	if orderType.Limit != nil {
		return OrderTypeWire{Limit: orderType.Limit}, nil
	} else if orderType.Trigger != nil {
		triggerPxStr, err := FloatToWire(orderType.Trigger.TriggerPx)
		if err != nil {
			return OrderTypeWire{}, err
		}
		return OrderTypeWire{
			Trigger: &TriggerOrderTypeWire{
				IsMarket:  orderType.Trigger.IsMarket,
				TriggerPx: triggerPxStr,
				Tpsl:      orderType.Trigger.Tpsl,
			},
		}, nil
	}
	return OrderTypeWire{}, fmt.Errorf("invalid order type")
}

// OrderRequestToOrderWire converts OrderRequest to OrderWire
func OrderRequestToOrderWire(order OrderRequest, asset int) (OrderWire, error) {
	priceStr, err := FloatToWire(order.LimitPx)
	if err != nil {
		return OrderWire{}, err
	}
	
	sizeStr, err := FloatToWire(order.Sz)
	if err != nil {
		return OrderWire{}, err
	}
	
	orderTypeWire, err := OrderTypeToWire(order.OrderType)
	if err != nil {
		return OrderWire{}, err
	}
	
	orderWire := OrderWire{
		A: asset,
		B: order.IsBuy,
		P: priceStr,
		S: sizeStr,
		R: order.ReduceOnly,
		T: orderTypeWire,
	}
	
	if order.Cloid != nil {
		cloidStr := order.Cloid.ToRaw()
		orderWire.C = &cloidStr
	}
	
	return orderWire, nil
}

// OrderWiresToOrderAction converts order wires to order action
func OrderWiresToOrderAction(orderWires []OrderWire, builder *BuilderInfo) map[string]interface{} {
	action := map[string]interface{}{
		"type":     "order",
		"orders":   orderWires,
		"grouping": "na",
	}
	if builder != nil {
		action["builder"] = builder
	}
	return action
}

// Signature represents a cryptographic signature
type Signature struct {
	R string `json:"r"`
	S string `json:"s"`
	V int    `json:"v"`
}

// SignL1Action signs an L1 action (placeholder implementation)
func SignL1Action(wallet *LocalAccount, action map[string]interface{}, activePool *string, nonce int64, expiresAfter *int64, isMainnet bool) (*Signature, error) {
	// This is a placeholder implementation
	// In a real implementation, you would:
	// 1. Create action hash
	// 2. Construct phantom agent
	// 3. Create L1 payload
	// 4. Sign with wallet
	
	// For now, return a dummy signature
	return &Signature{
		R: "0x0000000000000000000000000000000000000000000000000000000000000000",
		S: "0x0000000000000000000000000000000000000000000000000000000000000000",
		V: 27,
	}, nil
}

// SignUsdTransferAction signs a USD transfer action (placeholder implementation)
func SignUsdTransferAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	// Placeholder implementation
	return &Signature{
		R: "0x0000000000000000000000000000000000000000000000000000000000000000",
		S: "0x0000000000000000000000000000000000000000000000000000000000000000",
		V: 27,
	}, nil
}

// SignSpotTransferAction signs a spot transfer action (placeholder implementation)
func SignSpotTransferAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	// Placeholder implementation
	return &Signature{
		R: "0x0000000000000000000000000000000000000000000000000000000000000000",
		S: "0x0000000000000000000000000000000000000000000000000000000000000000",
		V: 27,
	}, nil
}

// SignWithdrawFromBridgeAction signs a withdraw from bridge action (placeholder implementation)
func SignWithdrawFromBridgeAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	// Placeholder implementation
	return &Signature{
		R: "0x0000000000000000000000000000000000000000000000000000000000000000",
		S: "0x0000000000000000000000000000000000000000000000000000000000000000",
		V: 27,
	}, nil
}

// SignUsdClassTransferAction signs a USD class transfer action (placeholder implementation)
func SignUsdClassTransferAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	// Placeholder implementation
	return &Signature{
		R: "0x0000000000000000000000000000000000000000000000000000000000000000",
		S: "0x0000000000000000000000000000000000000000000000000000000000000000",
		V: 27,
	}, nil
}

// SignSendAssetAction signs a send asset action (placeholder implementation)
func SignSendAssetAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	// Placeholder implementation
	return &Signature{
		R: "0x0000000000000000000000000000000000000000000000000000000000000000",
		S: "0x0000000000000000000000000000000000000000000000000000000000000000",
		V: 27,
	}, nil
}

// SignTokenDelegateAction signs a token delegate action (placeholder implementation)
func SignTokenDelegateAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	// Placeholder implementation
	return &Signature{
		R: "0x0000000000000000000000000000000000000000000000000000000000000000",
		S: "0x0000000000000000000000000000000000000000000000000000000000000000",
		V: 27,
	}, nil
}

// SignAgent signs an agent action (placeholder implementation)
func SignAgent(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	// Placeholder implementation
	return &Signature{
		R: "0x0000000000000000000000000000000000000000000000000000000000000000",
		S: "0x0000000000000000000000000000000000000000000000000000000000000000",
		V: 27,
	}, nil
}

// SignApproveBuilderFee signs an approve builder fee action (placeholder implementation)
func SignApproveBuilderFee(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	// Placeholder implementation
	return &Signature{
		R: "0x0000000000000000000000000000000000000000000000000000000000000000",
		S: "0x0000000000000000000000000000000000000000000000000000000000000000",
		V: 27,
	}, nil
}

// SignConvertToMultiSigUserAction signs a convert to multi sig user action (placeholder implementation)
func SignConvertToMultiSigUserAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool) (*Signature, error) {
	// Placeholder implementation
	return &Signature{
		R: "0x0000000000000000000000000000000000000000000000000000000000000000",
		S: "0x0000000000000000000000000000000000000000000000000000000000000000",
		V: 27,
	}, nil
}

// SignMultiSigAction signs a multi sig action (placeholder implementation)
func SignMultiSigAction(wallet *LocalAccount, action map[string]interface{}, isMainnet bool, vaultAddress *string, nonce int64, expiresAfter *int64) (*Signature, error) {
	// Placeholder implementation
	return &Signature{
		R: "0x0000000000000000000000000000000000000000000000000000000000000000",
		S: "0x0000000000000000000000000000000000000000000000000000000000000000",
		V: 27,
	}, nil
}


