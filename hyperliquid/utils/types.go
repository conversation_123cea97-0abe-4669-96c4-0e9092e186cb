package utils

import (
	"fmt"
	"strings"
)

// Any represents any type, equivalent to Python's Any
type Any interface{}

// Dict represents a dictionary/map type
type Dict map[string]interface{}

// List represents a list/slice type
type List []interface{}

// Optional represents an optional type
type Optional[T any] *T

// Tuple represents a tuple type
type Tuple[T, U any] struct {
	First  T
	Second U
}

// AssetInfo represents asset information
type AssetInfo struct {
	Name       string `json:"name"`
	SzDecimals int    `json:"szDecimals"`
}

// Meta represents metadata
type Meta struct {
	Universe []AssetInfo `json:"universe"`
}

// SpotAssetInfo represents spot asset information
type SpotAssetInfo struct {
	Name        string `json:"name"`
	Tokens      []int  `json:"tokens"`
	Index       int    `json:"index"`
	IsCanonical bool   `json:"isCanonical"`
}

// SpotTokenInfo represents spot token information
type SpotTokenInfo struct {
	Name        string  `json:"name"`
	SzDecimals  int     `json:"szDecimals"`
	WeiDecimals int     `json:"weiDecimals"`
	Index       int     `json:"index"`
	TokenID     string  `json:"tokenId"`
	IsCanonical bool    `json:"isCanonical"`
	EvmContract *string `json:"evmContract,omitempty"`
	FullName    *string `json:"fullName,omitempty"`
}

// SpotMeta represents spot metadata
type SpotMeta struct {
	Universe []SpotAssetInfo `json:"universe"`
	Tokens   []SpotTokenInfo `json:"tokens"`
}

// BuilderInfo represents builder information
type BuilderInfo struct {
	B string `json:"b"` // builder address
	F int    `json:"f"` // fee in tenths of basis points
}

// PerpDexSchemaInput represents perp dex schema input
type PerpDexSchemaInput struct {
	FullName         string  `json:"fullName"`
	CollateralToken  int     `json:"collateralToken"`
	OracleUpdater    *string `json:"oracleUpdater,omitempty"`
}

// Cloid represents a client order ID
type Cloid struct {
	rawCloid string
}

// NewCloid creates a new Cloid from a hex string
func NewCloid(rawCloid string) (*Cloid, error) {
	c := &Cloid{rawCloid: rawCloid}
	if err := c.validate(); err != nil {
		return nil, err
	}
	return c, nil
}

// CloidFromInt creates a Cloid from an integer
func CloidFromInt(cloid int) *Cloid {
	return &Cloid{rawCloid: fmt.Sprintf("0x%032x", cloid)}
}

// CloidFromStr creates a Cloid from a string
func CloidFromStr(cloid string) (*Cloid, error) {
	return NewCloid(cloid)
}

func (c *Cloid) validate() error {
	if !strings.HasPrefix(c.rawCloid, "0x") {
		return fmt.Errorf("cloid is not a hex string")
	}
	if len(c.rawCloid[2:]) != 32 {
		return fmt.Errorf("cloid is not 16 bytes")
	}
	return nil
}

func (c *Cloid) String() string {
	return c.rawCloid
}

func (c *Cloid) ToRaw() string {
	return c.rawCloid
}

// Tif represents time in force
type Tif string

const (
	TifAlo Tif = "Alo"
	TifIoc Tif = "Ioc"
	TifGtc Tif = "Gtc"
)

// Tpsl represents take profit / stop loss
type Tpsl string

const (
	TpslTp Tpsl = "tp"
	TpslSl Tpsl = "sl"
)

// LimitOrderType represents a limit order type
type LimitOrderType struct {
	Tif Tif `json:"tif"`
}

// TriggerOrderType represents a trigger order type
type TriggerOrderType struct {
	TriggerPx float64 `json:"triggerPx"`
	IsMarket  bool    `json:"isMarket"`
	Tpsl      Tpsl    `json:"tpsl"`
}

// TriggerOrderTypeWire represents a trigger order type for wire format
type TriggerOrderTypeWire struct {
	TriggerPx string `json:"triggerPx"`
	IsMarket  bool   `json:"isMarket"`
	Tpsl      Tpsl   `json:"tpsl"`
}

// OrderType represents an order type
type OrderType struct {
	Limit   *LimitOrderType   `json:"limit,omitempty"`
	Trigger *TriggerOrderType `json:"trigger,omitempty"`
}

// OrderTypeWire represents an order type for wire format
type OrderTypeWire struct {
	Limit   *LimitOrderType       `json:"limit,omitempty"`
	Trigger *TriggerOrderTypeWire `json:"trigger,omitempty"`
}

// OrderRequest represents an order request
type OrderRequest struct {
	Coin      string     `json:"coin"`
	IsBuy     bool       `json:"is_buy"`
	Sz        float64    `json:"sz"`
	LimitPx   float64    `json:"limit_px"`
	OrderType OrderType  `json:"order_type"`
	ReduceOnly bool      `json:"reduce_only"`
	Cloid     *Cloid     `json:"cloid,omitempty"`
}

// OidOrCloid represents either an order ID or client order ID
type OidOrCloid interface {
	isOidOrCloid()
}

type OidInt struct {
	Value int
}

func (o OidInt) isOidOrCloid() {}

type OidCloid struct {
	Value *Cloid
}

func (o OidCloid) isOidOrCloid() {}

// ModifyRequest represents a modify request
type ModifyRequest struct {
	Oid   OidOrCloid   `json:"oid"`
	Order OrderRequest `json:"order"`
}

// CancelRequest represents a cancel request
type CancelRequest struct {
	Coin string `json:"coin"`
	Oid  int    `json:"oid"`
}

// CancelByCloidRequest represents a cancel by cloid request
type CancelByCloidRequest struct {
	Coin  string `json:"coin"`
	Cloid *Cloid `json:"cloid"`
}

// OrderWire represents an order in wire format
type OrderWire struct {
	A int           `json:"a"`           // asset
	B bool          `json:"b"`           // is_buy
	P string        `json:"p"`           // price
	S string        `json:"s"`           // size
	R bool          `json:"r"`           // reduce_only
	T OrderTypeWire `json:"t"`           // order_type
	C *string       `json:"c,omitempty"` // cloid
}

// ScheduleCancelAction represents a schedule cancel action
type ScheduleCancelAction struct {
	Type string `json:"type"` // "scheduleCancel"
	Time *int   `json:"time,omitempty"`
}
