package utils

import (
	"fmt"
	"strings"
)

// Any represents any type, equivalent to Python's Any
type Any any

// Dict represents a dictionary/map type
type Dict map[string]any

// List represents a list/slice type
type List []any

// Optional represents an optional type
type Optional[T any] *T

// Tuple represents a tuple type
type Tuple[T, U any] struct {
	First  T
	Second U
}

// AssetInfo represents asset information
type AssetInfo struct {
	Name       string `json:"name"`
	SzDecimals int    `json:"szDecimals"`
}

// Meta represents metadata
type Meta struct {
	Universe []AssetInfo `json:"universe"`
}

// SpotAssetInfo represents spot asset information
type SpotAssetInfo struct {
	Name        string `json:"name"`
	Tokens      []int  `json:"tokens"`
	Index       int    `json:"index"`
	IsCanonical bool   `json:"isCanonical"`
}

// SpotTokenInfo represents spot token information
type SpotTokenInfo struct {
	Name        string  `json:"name"`
	SzDecimals  int     `json:"szDecimals"`
	WeiDecimals int     `json:"weiDecimals"`
	Index       int     `json:"index"`
	TokenID     string  `json:"tokenId"`
	IsCanonical bool    `json:"isCanonical"`
	EvmContract *string `json:"evmContract,omitempty"`
	FullName    *string `json:"fullName,omitempty"`
}

// SpotMeta represents spot metadata
type SpotMeta struct {
	Universe []SpotAssetInfo `json:"universe"`
	Tokens   []SpotTokenInfo `json:"tokens"`
}

// BuilderInfo represents builder information
type BuilderInfo struct {
	B string `json:"b"` // builder address
	F int    `json:"f"` // fee in tenths of basis points
}

// PerpDexSchemaInput represents perp dex schema input
type PerpDexSchemaInput struct {
	FullName         string  `json:"fullName"`
	CollateralToken  int     `json:"collateralToken"`
	OracleUpdater    *string `json:"oracleUpdater,omitempty"`
}

// Cloid represents a client order ID
type Cloid struct {
	rawCloid string
}

// NewCloid creates a new Cloid from a hex string
func NewCloid(rawCloid string) (*Cloid, error) {
	c := &Cloid{rawCloid: rawCloid}
	if err := c.validate(); err != nil {
		return nil, err
	}
	return c, nil
}

// CloidFromInt creates a Cloid from an integer
func CloidFromInt(cloid int) *Cloid {
	return &Cloid{rawCloid: fmt.Sprintf("0x%032x", cloid)}
}

// CloidFromStr creates a Cloid from a string
func CloidFromStr(cloid string) (*Cloid, error) {
	return NewCloid(cloid)
}

func (c *Cloid) validate() error {
	if !strings.HasPrefix(c.rawCloid, "0x") {
		return fmt.Errorf("cloid is not a hex string")
	}
	if len(c.rawCloid[2:]) != 32 {
		return fmt.Errorf("cloid is not 16 bytes")
	}
	return nil
}

func (c *Cloid) String() string {
	return c.rawCloid
}

func (c *Cloid) ToRaw() string {
	return c.rawCloid
}

// Tif represents time in force
type Tif string

const (
	TifAlo Tif = "Alo"
	TifIoc Tif = "Ioc"
	TifGtc Tif = "Gtc"
)

// Tpsl represents take profit / stop loss
type Tpsl string

const (
	TpslTp Tpsl = "tp"
	TpslSl Tpsl = "sl"
)

// LimitOrderType represents a limit order type
type LimitOrderType struct {
	Tif Tif `json:"tif"`
}

// TriggerOrderType represents a trigger order type
type TriggerOrderType struct {
	TriggerPx float64 `json:"triggerPx"`
	IsMarket  bool    `json:"isMarket"`
	Tpsl      Tpsl    `json:"tpsl"`
}

// TriggerOrderTypeWire represents a trigger order type for wire format
type TriggerOrderTypeWire struct {
	TriggerPx string `json:"triggerPx"`
	IsMarket  bool   `json:"isMarket"`
	Tpsl      Tpsl   `json:"tpsl"`
}

// OrderType represents an order type
type OrderType struct {
	Limit   *LimitOrderType   `json:"limit,omitempty"`
	Trigger *TriggerOrderType `json:"trigger,omitempty"`
}

// OrderTypeWire represents an order type for wire format
type OrderTypeWire struct {
	Limit   *LimitOrderType       `json:"limit,omitempty"`
	Trigger *TriggerOrderTypeWire `json:"trigger,omitempty"`
}

// OrderRequest represents an order request
type OrderRequest struct {
	Coin      string     `json:"coin"`
	IsBuy     bool       `json:"is_buy"`
	Sz        float64    `json:"sz"`
	LimitPx   float64    `json:"limit_px"`
	OrderType OrderType  `json:"order_type"`
	ReduceOnly bool      `json:"reduce_only"`
	Cloid     *Cloid     `json:"cloid,omitempty"`
}

// OidOrCloid represents either an order ID or client order ID
type OidOrCloid interface {
	isOidOrCloid()
}

type OidInt struct {
	Value int
}

func (o OidInt) isOidOrCloid() {}

type OidCloid struct {
	Value *Cloid
}

func (o OidCloid) isOidOrCloid() {}

// ModifyRequest represents a modify request
type ModifyRequest struct {
	Oid   OidOrCloid   `json:"oid"`
	Order OrderRequest `json:"order"`
}

// CancelRequest represents a cancel request
type CancelRequest struct {
	Coin string `json:"coin"`
	Oid  int    `json:"oid"`
}

// CancelByCloidRequest represents a cancel by cloid request
type CancelByCloidRequest struct {
	Coin  string `json:"coin"`
	Cloid *Cloid `json:"cloid"`
}

// OrderWire represents an order in wire format
type OrderWire struct {
	A int           `json:"a"`           // asset
	B bool          `json:"b"`           // is_buy
	P string        `json:"p"`           // price
	S string        `json:"s"`           // size
	R bool          `json:"r"`           // reduce_only
	T OrderTypeWire `json:"t"`           // order_type
	C *string       `json:"c,omitempty"` // cloid
}

// ScheduleCancelAction represents a schedule cancel action
type ScheduleCancelAction struct {
	Type string `json:"type"` // "scheduleCancel"
	Time *int   `json:"time,omitempty"`
}

// Websocket subscription types
type AllMidsSubscription struct {
	Type string `json:"type"` // "allMids"
}

type BboSubscription struct {
	Type string `json:"type"` // "bbo"
	Coin string `json:"coin"`
}

type L2BookSubscription struct {
	Type string `json:"type"` // "l2Book"
	Coin string `json:"coin"`
}

type TradesSubscription struct {
	Type string `json:"type"` // "trades"
	Coin string `json:"coin"`
}

type UserEventsSubscription struct {
	Type string `json:"type"` // "userEvents"
	User string `json:"user"`
}

type UserFillsSubscription struct {
	Type string `json:"type"` // "userFills"
	User string `json:"user"`
}

type CandleSubscription struct {
	Type     string `json:"type"` // "candle"
	Coin     string `json:"coin"`
	Interval string `json:"interval"`
}

type OrderUpdatesSubscription struct {
	Type string `json:"type"` // "orderUpdates"
	User string `json:"user"`
}

type UserFundingsSubscription struct {
	Type string `json:"type"` // "userFundings"
	User string `json:"user"`
}

type UserNonFundingLedgerUpdatesSubscription struct {
	Type string `json:"type"` // "userNonFundingLedgerUpdates"
	User string `json:"user"`
}

type WebData2Subscription struct {
	Type string `json:"type"` // "webData2"
	User string `json:"user"`
}

type ActiveAssetCtxSubscription struct {
	Type string `json:"type"` // "activeAssetCtx"
	Coin string `json:"coin"`
}

type ActiveAssetDataSubscription struct {
	Type string `json:"type"` // "activeAssetData"
	User string `json:"user"`
	Coin string `json:"coin"`
}

// Subscription represents any subscription type
type Subscription interface {
	GetType() string
}

func (s AllMidsSubscription) GetType() string                           { return s.Type }
func (s BboSubscription) GetType() string                               { return s.Type }
func (s L2BookSubscription) GetType() string                            { return s.Type }
func (s TradesSubscription) GetType() string                            { return s.Type }
func (s UserEventsSubscription) GetType() string                        { return s.Type }
func (s UserFillsSubscription) GetType() string                         { return s.Type }
func (s CandleSubscription) GetType() string                            { return s.Type }
func (s OrderUpdatesSubscription) GetType() string                      { return s.Type }
func (s UserFundingsSubscription) GetType() string                      { return s.Type }
func (s UserNonFundingLedgerUpdatesSubscription) GetType() string       { return s.Type }
func (s WebData2Subscription) GetType() string                          { return s.Type }
func (s ActiveAssetCtxSubscription) GetType() string                    { return s.Type }
func (s ActiveAssetDataSubscription) GetType() string                   { return s.Type }

// Websocket message types
type Side string

const (
	SideA Side = "A" // Ask
	SideB Side = "B" // Bid
)

type L2Level struct {
	Px string `json:"px"`
	Sz string `json:"sz"`
	N  int    `json:"n"`
}

type AllMidsData struct {
	Mids map[string]string `json:"mids"`
}

type AllMidsMsg struct {
	Channel string       `json:"channel"` // "allMids"
	Data    AllMidsData  `json:"data"`
}

type L2BookData struct {
	Coin   string      `json:"coin"`
	Levels [2][]L2Level `json:"levels"` // [bids, asks]
	Time   int64       `json:"time"`
}

type L2BookMsg struct {
	Channel string     `json:"channel"` // "l2Book"
	Data    L2BookData `json:"data"`
}

type BboData struct {
	Coin string     `json:"coin"`
	Time int64      `json:"time"`
	Bbo  [2]*L2Level `json:"bbo"` // [bid, ask]
}

type BboMsg struct {
	Channel string  `json:"channel"` // "bbo"
	Data    BboData `json:"data"`
}

type PongMsg struct {
	Channel string `json:"channel"` // "pong"
}

type Trade struct {
	Coin string `json:"coin"`
	Side Side   `json:"side"`
	Px   string `json:"px"`
	Sz   int    `json:"sz"`
	Hash string `json:"hash"`
	Time int64  `json:"time"`
}

type TradesMsg struct {
	Channel string  `json:"channel"` // "trades"
	Data    []Trade `json:"data"`
}

type Fill struct {
	Coin          string `json:"coin"`
	Px            string `json:"px"`
	Sz            string `json:"sz"`
	Side          Side   `json:"side"`
	Time          int64  `json:"time"`
	StartPosition string `json:"startPosition"`
	Dir           string `json:"dir"`
	ClosedPnl     string `json:"closedPnl"`
	Hash          string `json:"hash"`
	Oid           int    `json:"oid"`
	Crossed       bool   `json:"crossed"`
	Fee           string `json:"fee"`
	Tid           int    `json:"tid"`
	FeeToken      string `json:"feeToken"`
}

type UserEventsData struct {
	Fills []Fill `json:"fills,omitempty"`
}

type UserEventsMsg struct {
	Channel string         `json:"channel"` // "user"
	Data    UserEventsData `json:"data"`
}

type UserFillsData struct {
	User       string `json:"user"`
	IsSnapshot bool   `json:"isSnapshot"`
	Fills      []Fill `json:"fills"`
}

type UserFillsMsg struct {
	Channel string        `json:"channel"` // "userFills"
	Data    UserFillsData `json:"data"`
}

type PerpAssetCtx struct {
	Funding      string    `json:"funding"`
	OpenInterest string    `json:"openInterest"`
	PrevDayPx    string    `json:"prevDayPx"`
	DayNtlVlm    string    `json:"dayNtlVlm"`
	Premium      string    `json:"premium"`
	OraclePx     string    `json:"oraclePx"`
	MarkPx       string    `json:"markPx"`
	MidPx        *string   `json:"midPx,omitempty"`
	ImpactPxs    *[2]string `json:"impactPxs,omitempty"`
	DayBaseVlm   string    `json:"dayBaseVlm"`
}

type SpotAssetCtx struct {
	DayNtlVlm         string  `json:"dayNtlVlm"`
	MarkPx            string  `json:"markPx"`
	MidPx             *string `json:"midPx,omitempty"`
	PrevDayPx         string  `json:"prevDayPx"`
	CirculatingSupply string  `json:"circulatingSupply"`
	Coin              string  `json:"coin"`
}

type ActiveAssetCtx struct {
	Coin string       `json:"coin"`
	Ctx  PerpAssetCtx `json:"ctx"`
}

type ActiveSpotAssetCtx struct {
	Coin string       `json:"coin"`
	Ctx  SpotAssetCtx `json:"ctx"`
}

type ActiveAssetCtxMsg struct {
	Channel string         `json:"channel"` // "activeAssetCtx"
	Data    ActiveAssetCtx `json:"data"`
}

type ActiveSpotAssetCtxMsg struct {
	Channel string             `json:"channel"` // "activeSpotAssetCtx"
	Data    ActiveSpotAssetCtx `json:"data"`
}

type CrossLeverage struct {
	Type  string `json:"type"`  // "cross"
	Value int    `json:"value"`
}

type IsolatedLeverage struct {
	Type   string `json:"type"`   // "isolated"
	Value  int    `json:"value"`
	RawUsd string `json:"rawUsd"`
}

type Leverage interface {
	GetLeverageType() string
}

func (l CrossLeverage) GetLeverageType() string    { return l.Type }
func (l IsolatedLeverage) GetLeverageType() string { return l.Type }

type ActiveAssetData struct {
	User              string     `json:"user"`
	Coin              string     `json:"coin"`
	Leverage          Leverage   `json:"leverage"`
	MaxTradeSzs       [2]string  `json:"maxTradeSzs"`
	AvailableToTrade  [2]string  `json:"availableToTrade"`
	MarkPx            string     `json:"markPx"`
}

type ActiveAssetDataMsg struct {
	Channel string          `json:"channel"` // "activeAssetData"
	Data    ActiveAssetData `json:"data"`
}

type OtherWsMsg struct {
	Channel string `json:"channel"`
	Data    Any    `json:"data,omitempty"`
}

// WsMsg represents any websocket message
type WsMsg interface {
	GetChannel() string
}

func (m AllMidsMsg) GetChannel() string             { return m.Channel }
func (m BboMsg) GetChannel() string                 { return m.Channel }
func (m L2BookMsg) GetChannel() string              { return m.Channel }
func (m TradesMsg) GetChannel() string              { return m.Channel }
func (m UserEventsMsg) GetChannel() string          { return m.Channel }
func (m PongMsg) GetChannel() string                { return m.Channel }
func (m UserFillsMsg) GetChannel() string           { return m.Channel }
func (m ActiveAssetCtxMsg) GetChannel() string      { return m.Channel }
func (m ActiveSpotAssetCtxMsg) GetChannel() string  { return m.Channel }
func (m ActiveAssetDataMsg) GetChannel() string     { return m.Channel }
func (m OtherWsMsg) GetChannel() string             { return m.Channel }
