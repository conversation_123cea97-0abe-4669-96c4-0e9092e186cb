package hyperliquid

import (
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/yudaprama/hyperliquid-go-sdk/utils"
)

// Exchange represents the Exchange API client
type Exchange struct {
	*API
	Wallet         *utils.LocalAccount
	vaultAddress   *string
	accountAddress *string
	info           *Info
	expiresAfter   *int64
}

const (
	// DefaultSlippage represents the default max slippage for market orders (5%)
	DefaultSlippage = 0.05
)

// NewExchange creates a new Exchange instance
func NewExchange(
	wallet *utils.LocalAccount,
	baseURL *string,
	meta *utils.Meta,
	vaultAddress *string,
	accountAddress *string,
	spotMeta *utils.SpotMeta,
	perpDexs []string,
) *Exchange {
	var url string
	if baseURL != nil {
		url = *baseURL
	} else {
		url = utils.MainnetAPIURL
	}
	
	api := NewAPI(url)
	info := NewInfo(url, true, meta, spotMeta, perpDexs)
	
	return &Exchange{
		API:            api,
		Wallet:         wallet,
		vaultAddress:   vaultAddress,
		accountAddress: accountAddress,
		info:           info,
		expiresAfter:   nil,
	}
}

// postAction posts an action with signature and nonce
func (e *Exchange) postAction(action map[string]interface{}, signature *utils.Signature, nonce int64) (utils.Any, error) {
	payload := map[string]interface{}{
		"action":    action,
		"nonce":     nonce,
		"signature": signature,
	}
	
	// Add vaultAddress if not usdClassTransfer
	if actionType, ok := action["type"].(string); !ok || actionType != "usdClassTransfer" {
		payload["vaultAddress"] = e.vaultAddress
	}
	
	if e.expiresAfter != nil {
		payload["expiresAfter"] = *e.expiresAfter
	}
	
	e.logger.Debugf("Posting action: %+v", payload)
	return e.Post("/exchange", payload)
}

// slippagePrice calculates slippage price
func (e *Exchange) slippagePrice(name string, isBuy bool, slippage float64, px *float64) (float64, error) {
	coin, exists := e.info.nameToCoin[name]
	if !exists {
		return 0, fmt.Errorf("coin not found: %s", name)
	}
	
	var price float64
	if px != nil {
		price = *px
	} else {
		// Get midprice
		mids, err := e.info.AllMids()
		if err != nil {
			return 0, fmt.Errorf("failed to get mids: %w", err)
		}
		
		midStr, exists := mids[coin]
		if !exists {
			return 0, fmt.Errorf("mid price not found for coin: %s", coin)
		}
		
		var err2 error
		price, err2 = strconv.ParseFloat(midStr, 64)
		if err2 != nil {
			return 0, fmt.Errorf("failed to parse mid price: %w", err2)
		}
	}
	
	asset := e.info.NameToAsset(coin)
	// spot assets start at 10000
	isSpot := asset >= 10000
	
	// Calculate slippage
	if isBuy {
		price *= (1 + slippage)
	} else {
		price *= (1 - slippage)
	}
	
	// Round px to 5 significant figures and 6 decimals for perps, 8 decimals for spot
	decimals := 6
	if isSpot {
		decimals = 8
	}
	
	szDecimals, exists := e.info.assetToSzDecimals[asset]
	if exists {
		decimals -= szDecimals
	}
	
	// Round to specified precision
	multiplier := math.Pow10(decimals)
	return math.Round(price*multiplier) / multiplier, nil
}

// SetExpiresAfter sets the expires after timestamp
func (e *Exchange) SetExpiresAfter(expiresAfter *int64) {
	e.expiresAfter = expiresAfter
}

// Order places a single order
func (e *Exchange) Order(
	name string,
	isBuy bool,
	sz float64,
	limitPx float64,
	orderType utils.OrderType,
	reduceOnly bool,
	cloid *utils.Cloid,
	builder *utils.BuilderInfo,
) (utils.Any, error) {
	order := utils.OrderRequest{
		Coin:       name,
		IsBuy:      isBuy,
		Sz:         sz,
		LimitPx:    limitPx,
		OrderType:  orderType,
		ReduceOnly: reduceOnly,
		Cloid:      cloid,
	}
	
	return e.BulkOrders([]utils.OrderRequest{order}, builder)
}

// BulkOrders places multiple orders
func (e *Exchange) BulkOrders(orderRequests []utils.OrderRequest, builder *utils.BuilderInfo) (utils.Any, error) {
	var orderWires []utils.OrderWire
	
	for _, order := range orderRequests {
		asset := e.info.NameToAsset(order.Coin)
		if asset == 0 {
			return nil, fmt.Errorf("asset not found for coin: %s", order.Coin)
		}
		
		orderWire, err := utils.OrderRequestToOrderWire(order, asset)
		if err != nil {
			return nil, fmt.Errorf("failed to convert order to wire: %w", err)
		}
		
		orderWires = append(orderWires, orderWire)
	}
	
	timestamp := utils.GetTimestampMs()
	
	if builder != nil {
		// Convert builder address to lowercase
		builder.B = strings.ToLower(builder.B)
	}
	
	orderAction := utils.OrderWiresToOrderAction(orderWires, builder)
	
	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignL1Action(
		e.Wallet,
		orderAction,
		e.vaultAddress,
		timestamp,
		e.expiresAfter,
		isMainnet,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}
	
	return e.postAction(orderAction, signature, timestamp)
}

// ModifyOrder modifies a single order
func (e *Exchange) ModifyOrder(
	oid utils.OidOrCloid,
	name string,
	isBuy bool,
	sz float64,
	limitPx float64,
	orderType utils.OrderType,
	reduceOnly bool,
	cloid *utils.Cloid,
) (utils.Any, error) {
	modify := utils.ModifyRequest{
		Oid: oid,
		Order: utils.OrderRequest{
			Coin:       name,
			IsBuy:      isBuy,
			Sz:         sz,
			LimitPx:    limitPx,
			OrderType:  orderType,
			ReduceOnly: reduceOnly,
			Cloid:      cloid,
		},
	}
	
	return e.BulkModifyOrdersNew([]utils.ModifyRequest{modify})
}

// BulkModifyOrdersNew modifies multiple orders
func (e *Exchange) BulkModifyOrdersNew(modifyRequests []utils.ModifyRequest) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()
	
	var modifyWires []map[string]interface{}
	
	for _, modify := range modifyRequests {
		asset := e.info.NameToAsset(modify.Order.Coin)
		if asset == 0 {
			return nil, fmt.Errorf("asset not found for coin: %s", modify.Order.Coin)
		}
		
		orderWire, err := utils.OrderRequestToOrderWire(modify.Order, asset)
		if err != nil {
			return nil, fmt.Errorf("failed to convert order to wire: %w", err)
		}
		
		var oidValue interface{}
		switch v := modify.Oid.(type) {
		case utils.OidInt:
			oidValue = v.Value
		case utils.OidCloid:
			if v.Value != nil {
				oidValue = v.Value.ToRaw()
			}
		default:
			return nil, fmt.Errorf("invalid oid type")
		}
		
		modifyWire := map[string]interface{}{
			"oid":   oidValue,
			"order": orderWire,
		}
		
		modifyWires = append(modifyWires, modifyWire)
	}
	
	modifyAction := map[string]interface{}{
		"type":     "batchModify",
		"modifies": modifyWires,
	}
	
	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignL1Action(
		e.Wallet,
		modifyAction,
		e.vaultAddress,
		timestamp,
		e.expiresAfter,
		isMainnet,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}
	
	return e.postAction(modifyAction, signature, timestamp)
}

// MarketOpen places a market order to open a position
func (e *Exchange) MarketOpen(
	name string,
	isBuy bool,
	sz float64,
	px *float64,
	slippage float64,
	cloid *utils.Cloid,
	builder *utils.BuilderInfo,
) (utils.Any, error) {
	if slippage == 0 {
		slippage = DefaultSlippage
	}
	
	// Get aggressive market price
	price, err := e.slippagePrice(name, isBuy, slippage, px)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate slippage price: %w", err)
	}
	
	// Market order is an aggressive limit order IoC
	orderType := utils.OrderType{
		Limit: &utils.LimitOrderType{
			Tif: utils.TifIoc,
		},
	}
	
	return e.Order(name, isBuy, sz, price, orderType, false, cloid, builder)
}

// MarketClose places a market order to close a position
func (e *Exchange) MarketClose(
	coin string,
	sz *float64,
	px *float64,
	slippage float64,
	cloid *utils.Cloid,
	builder *utils.BuilderInfo,
) (utils.Any, error) {
	if slippage == 0 {
		slippage = DefaultSlippage
	}

	address := e.Wallet.Address
	if e.accountAddress != nil {
		address = *e.accountAddress
	}
	if e.vaultAddress != nil {
		address = *e.vaultAddress
	}

	userState, err := e.info.UserState(address)
	if err != nil {
		return nil, fmt.Errorf("failed to get user state: %w", err)
	}

	// Parse user state to find position
	userStateMap, ok := userState.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid user state format")
	}

	assetPositions, ok := userStateMap["assetPositions"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid asset positions format")
	}

	for _, pos := range assetPositions {
		posMap, ok := pos.(map[string]interface{})
		if !ok {
			continue
		}

		position, ok := posMap["position"].(map[string]interface{})
		if !ok {
			continue
		}

		positionCoin, ok := position["coin"].(string)
		if !ok || positionCoin != coin {
			continue
		}

		sziStr, ok := position["szi"].(string)
		if !ok {
			continue
		}

		szi, err := strconv.ParseFloat(sziStr, 64)
		if err != nil {
			continue
		}

		var size float64
		if sz != nil {
			size = *sz
		} else {
			size = math.Abs(szi)
		}

		isBuy := szi < 0

		// Get aggressive market price
		price, err := e.slippagePrice(coin, isBuy, slippage, px)
		if err != nil {
			return nil, fmt.Errorf("failed to calculate slippage price: %w", err)
		}

		// Market order is an aggressive limit order IoC
		orderType := utils.OrderType{
			Limit: &utils.LimitOrderType{
				Tif: utils.TifIoc,
			},
		}

		return e.Order(coin, isBuy, size, price, orderType, true, cloid, builder)
	}

	return nil, fmt.Errorf("position not found for coin: %s", coin)
}

// Cancel cancels a single order
func (e *Exchange) Cancel(name string, oid int) (utils.Any, error) {
	cancelRequest := utils.CancelRequest{
		Coin: name,
		Oid:  oid,
	}
	return e.BulkCancel([]utils.CancelRequest{cancelRequest})
}

// CancelByCloid cancels a single order by client order ID
func (e *Exchange) CancelByCloid(name string, cloid *utils.Cloid) (utils.Any, error) {
	cancelRequest := utils.CancelByCloidRequest{
		Coin:  name,
		Cloid: cloid,
	}
	return e.BulkCancelByCloid([]utils.CancelByCloidRequest{cancelRequest})
}

// BulkCancel cancels multiple orders
func (e *Exchange) BulkCancel(cancelRequests []utils.CancelRequest) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	var cancels []map[string]interface{}
	for _, cancel := range cancelRequests {
		asset := e.info.NameToAsset(cancel.Coin)
		if asset == 0 {
			return nil, fmt.Errorf("asset not found for coin: %s", cancel.Coin)
		}

		cancelWire := map[string]interface{}{
			"a": asset,
			"o": cancel.Oid,
		}
		cancels = append(cancels, cancelWire)
	}

	cancelAction := map[string]interface{}{
		"type":    "cancel",
		"cancels": cancels,
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignL1Action(
		e.Wallet,
		cancelAction,
		e.vaultAddress,
		timestamp,
		e.expiresAfter,
		isMainnet,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(cancelAction, signature, timestamp)
}

// BulkCancelByCloid cancels multiple orders by client order ID
func (e *Exchange) BulkCancelByCloid(cancelRequests []utils.CancelByCloidRequest) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	var cancels []map[string]interface{}
	for _, cancel := range cancelRequests {
		asset := e.info.NameToAsset(cancel.Coin)
		if asset == 0 {
			return nil, fmt.Errorf("asset not found for coin: %s", cancel.Coin)
		}

		cancelWire := map[string]interface{}{
			"asset": asset,
			"cloid": cancel.Cloid.ToRaw(),
		}
		cancels = append(cancels, cancelWire)
	}

	cancelAction := map[string]interface{}{
		"type":    "cancelByCloid",
		"cancels": cancels,
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignL1Action(
		e.Wallet,
		cancelAction,
		e.vaultAddress,
		timestamp,
		e.expiresAfter,
		isMainnet,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(cancelAction, signature, timestamp)
}

// ScheduleCancel schedules a time to cancel all open orders
func (e *Exchange) ScheduleCancel(time *int) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	scheduleCancelAction := map[string]interface{}{
		"type": "scheduleCancel",
	}

	if time != nil {
		scheduleCancelAction["time"] = *time
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignL1Action(
		e.Wallet,
		scheduleCancelAction,
		e.vaultAddress,
		timestamp,
		e.expiresAfter,
		isMainnet,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(scheduleCancelAction, signature, timestamp)
}

// UpdateLeverage updates leverage for an asset
func (e *Exchange) UpdateLeverage(leverage int, name string, isCross bool) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	asset := e.info.NameToAsset(name)
	if asset == 0 {
		return nil, fmt.Errorf("asset not found for name: %s", name)
	}

	updateLeverageAction := map[string]interface{}{
		"type":     "updateLeverage",
		"asset":    asset,
		"isCross":  isCross,
		"leverage": leverage,
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignL1Action(
		e.Wallet,
		updateLeverageAction,
		e.vaultAddress,
		timestamp,
		e.expiresAfter,
		isMainnet,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(updateLeverageAction, signature, timestamp)
}

// UpdateIsolatedMargin updates isolated margin for an asset
func (e *Exchange) UpdateIsolatedMargin(amount float64, name string) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	asset := e.info.NameToAsset(name)
	if asset == 0 {
		return nil, fmt.Errorf("asset not found for name: %s", name)
	}

	amountInt, err := utils.FloatToUsdInt(amount)
	if err != nil {
		return nil, fmt.Errorf("failed to convert amount to USD int: %w", err)
	}

	updateIsolatedMarginAction := map[string]interface{}{
		"type":  "updateIsolatedMargin",
		"asset": asset,
		"isBuy": true,
		"ntli":  amountInt,
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignL1Action(
		e.Wallet,
		updateIsolatedMarginAction,
		e.vaultAddress,
		timestamp,
		e.expiresAfter,
		isMainnet,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(updateIsolatedMarginAction, signature, timestamp)
}

// SetReferrer sets a referrer code
func (e *Exchange) SetReferrer(code string) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	setReferrerAction := map[string]interface{}{
		"type": "setReferrer",
		"code": code,
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignL1Action(
		e.Wallet,
		setReferrerAction,
		nil, // vaultAddress is nil for setReferrer
		timestamp,
		e.expiresAfter,
		isMainnet,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(setReferrerAction, signature, timestamp)
}

// CreateSubAccount creates a sub account
func (e *Exchange) CreateSubAccount(name string) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	createSubAccountAction := map[string]interface{}{
		"type": "createSubAccount",
		"name": name,
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignL1Action(
		e.Wallet,
		createSubAccountAction,
		nil, // vaultAddress is nil for createSubAccount
		timestamp,
		e.expiresAfter,
		isMainnet,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(createSubAccountAction, signature, timestamp)
}

// UsdClassTransfer transfers USD between perp and spot
func (e *Exchange) UsdClassTransfer(amount float64, toPerp bool) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	strAmount := fmt.Sprintf("%g", amount)
	if e.vaultAddress != nil {
		strAmount += fmt.Sprintf(" subaccount:%s", *e.vaultAddress)
	}

	action := map[string]interface{}{
		"type":   "usdClassTransfer",
		"amount": strAmount,
		"toPerp": toPerp,
		"nonce":  timestamp,
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignUsdClassTransferAction(e.Wallet, action, isMainnet)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(action, signature, timestamp)
}

// UsdTransfer transfers USD to another address
func (e *Exchange) UsdTransfer(amount float64, destination string) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	action := map[string]interface{}{
		"destination": destination,
		"amount":      fmt.Sprintf("%g", amount),
		"time":        timestamp,
		"type":        "usdSend",
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignUsdTransferAction(e.Wallet, action, isMainnet)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(action, signature, timestamp)
}

// SpotTransfer transfers spot tokens to another address
func (e *Exchange) SpotTransfer(amount float64, destination string, token string) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	action := map[string]interface{}{
		"destination": destination,
		"amount":      fmt.Sprintf("%g", amount),
		"token":       token,
		"time":        timestamp,
		"type":        "spotSend",
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignSpotTransferAction(e.Wallet, action, isMainnet)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(action, signature, timestamp)
}

// VaultUsdTransfer transfers USD to/from a vault
func (e *Exchange) VaultUsdTransfer(vaultAddress string, isDeposit bool, usd int) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	vaultTransferAction := map[string]interface{}{
		"type":         "vaultTransfer",
		"vaultAddress": vaultAddress,
		"isDeposit":    isDeposit,
		"usd":          usd,
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignL1Action(
		e.Wallet,
		vaultTransferAction,
		nil, // vaultAddress is nil for vault transfers
		timestamp,
		e.expiresAfter,
		isMainnet,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(vaultTransferAction, signature, timestamp)
}

// WithdrawFromBridge withdraws from bridge
func (e *Exchange) WithdrawFromBridge(amount float64, destination string) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	action := map[string]interface{}{
		"destination": destination,
		"amount":      fmt.Sprintf("%g", amount),
		"time":        timestamp,
		"type":        "withdraw3",
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignWithdrawFromBridgeAction(e.Wallet, action, isMainnet)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(action, signature, timestamp)
}

// ApproveAgent approves an agent
func (e *Exchange) ApproveAgent(name *string) (utils.Any, string, error) {
	// Generate a random agent key (simplified implementation)
	agentKey := "0x" + strings.Repeat("0", 64) // placeholder

	// Create account from key (simplified)
	agentAddress := "0x" + strings.Repeat("1", 40) // placeholder

	timestamp := utils.GetTimestampMs()
	isMainnet := e.BaseURL == utils.MainnetAPIURL

	action := map[string]interface{}{
		"type":         "approveAgent",
		"agentAddress": agentAddress,
		"nonce":        timestamp,
	}

	if name != nil {
		action["agentName"] = *name
	} else {
		action["agentName"] = ""
	}

	signature, err := utils.SignAgent(e.Wallet, action, isMainnet)
	if err != nil {
		return nil, "", fmt.Errorf("failed to sign action: %w", err)
	}

	if name == nil {
		delete(action, "agentName")
	}

	result, err := e.postAction(action, signature, timestamp)
	return result, agentKey, err
}

// ApproveBuilderFee approves builder fee
func (e *Exchange) ApproveBuilderFee(builder string, maxFeeRate string) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	action := map[string]interface{}{
		"maxFeeRate": maxFeeRate,
		"builder":    builder,
		"nonce":      timestamp,
		"type":       "approveBuilderFee",
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignApproveBuilderFee(e.Wallet, action, isMainnet)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(action, signature, timestamp)
}

// ConvertToMultiSigUser converts to multi sig user
func (e *Exchange) ConvertToMultiSigUser(authorizedUsers []string, threshold int) (utils.Any, error) {
	timestamp := utils.GetTimestampMs()

	// Sort authorized users
	// Note: In Go, we'd typically use sort.Strings() but for simplicity keeping as is

	signers := map[string]interface{}{
		"authorizedUsers": authorizedUsers,
		"threshold":       threshold,
	}

	signersJSON, err := json.Marshal(signers)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal signers: %w", err)
	}

	action := map[string]interface{}{
		"type":    "convertToMultiSigUser",
		"signers": string(signersJSON),
		"nonce":   timestamp,
	}

	isMainnet := e.BaseURL == utils.MainnetAPIURL
	signature, err := utils.SignConvertToMultiSigUserAction(e.Wallet, action, isMainnet)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	return e.postAction(action, signature, timestamp)
}
