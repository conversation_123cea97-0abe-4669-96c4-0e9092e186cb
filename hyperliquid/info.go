package hyperliquid

import (
	"fmt"

	"github.com/yudaprama/hyperliquid-go-sdk/utils"
)

// Info represents the Info API client
type Info struct {
	*API
	coinToAsset        map[string]int
	nameToCoin         map[string]string
	assetToSzDecimals  map[int]int
}

// NewInfo creates a new Info instance
func NewInfo(baseURL string, skipWs bool, meta *utils.Meta, spotMeta *utils.SpotMeta, perpDexs []string) *Info {
	api := NewAPI(baseURL)
	
	info := &Info{
		API:               api,
		coinToAsset:       make(map[string]int),
		nameToCoin:        make(map[string]string),
		assetToSzDecimals: make(map[int]int),
	}
	
	// Initialize spot metadata
	if spotMeta == nil {
		var err error
		spotMeta, err = info.SpotMeta()
		if err != nil {
			// Handle error appropriately
			info.logger.Errorf("Failed to get spot meta: %v", err)
		}
	}
	
	if spotMeta != nil {
		// Process spot assets (start at 10000)
		for _, spotInfo := range spotMeta.Universe {
			asset := spotInfo.Index + 10000
			info.coinToAsset[spotInfo.Name] = asset
			info.nameToCoin[spotInfo.Name] = spotInfo.Name
			
			if len(spotInfo.Tokens) >= 2 {
				baseToken := spotInfo.Tokens[0]
				quoteToken := spotInfo.Tokens[1]
				
				if baseToken < len(spotMeta.Tokens) && quoteToken < len(spotMeta.Tokens) {
					baseInfo := spotMeta.Tokens[baseToken]
					quoteInfo := spotMeta.Tokens[quoteToken]
					info.assetToSzDecimals[asset] = baseInfo.SzDecimals
					
					name := fmt.Sprintf("%s/%s", baseInfo.Name, quoteInfo.Name)
					if _, exists := info.nameToCoin[name]; !exists {
						info.nameToCoin[name] = spotInfo.Name
					}
				}
			}
		}
	}
	
	// Process perp dexs
	perpDexToOffset := map[string]int{"": 0}
	if perpDexs == nil {
		perpDexs = []string{""}
	} else {
		// Handle builder-deployed perp dexs (start at 110000)
		for i, perpDex := range perpDexs[1:] {
			perpDexToOffset[perpDex] = 110000 + i*10000
		}
	}
	
	for _, perpDex := range perpDexs {
		offset := perpDexToOffset[perpDex]
		if perpDex == "" && meta != nil {
			info.setPerpMeta(meta, 0)
		} else {
			freshMeta, err := info.Meta(perpDex)
			if err != nil {
				info.logger.Errorf("Failed to get meta for dex %s: %v", perpDex, err)
				continue
			}
			info.setPerpMeta(freshMeta, offset)
		}
	}
	
	return info
}

func (info *Info) setPerpMeta(meta *utils.Meta, offset int) {
	for asset, assetInfo := range meta.Universe {
		adjustedAsset := asset + offset
		info.coinToAsset[assetInfo.Name] = adjustedAsset
		info.nameToCoin[assetInfo.Name] = assetInfo.Name
		info.assetToSzDecimals[adjustedAsset] = assetInfo.SzDecimals
	}
}

// NameToAsset converts a name to asset ID
func (info *Info) NameToAsset(name string) int {
	if asset, exists := info.coinToAsset[name]; exists {
		return asset
	}
	return 0
}

// UserState retrieves user state for a given address
func (info *Info) UserState(address string) (utils.Any, error) {
	payload := map[string]any{
		"type": "clearinghouseState",
		"user": address,
	}
	return info.Post("/info", payload)
}

// SpotUserState retrieves spot user state for a given address
func (info *Info) SpotUserState(address string) (utils.Any, error) {
	payload := map[string]any{
		"type": "spotClearinghouseState",
		"user": address,
	}
	return info.Post("/info", payload)
}

// AllMids retrieves all mids for all actively traded coins
func (info *Info) AllMids(dex ...string) (map[string]string, error) {
	dexName := ""
	if len(dex) > 0 {
		dexName = dex[0]
	}
	
	payload := map[string]interface{}{
		"type": "allMids",
		"dex":  dexName,
	}
	
	result, err := info.Post("/info", payload)
	if err != nil {
		return nil, err
	}
	
	// Convert result to map[string]string
	if midsMap, ok := result.(map[string]interface{}); ok {
		mids := make(map[string]string)
		for k, v := range midsMap {
			if strVal, ok := v.(string); ok {
				mids[k] = strVal
			}
		}
		return mids, nil
	}
	
	return nil, fmt.Errorf("unexpected response format")
}

// Meta retrieves exchange perp metadata
func (info *Info) Meta(dex ...string) (*utils.Meta, error) {
	dexName := ""
	if len(dex) > 0 {
		dexName = dex[0]
	}
	
	payload := map[string]interface{}{
		"type": "meta",
		"dex":  dexName,
	}
	
	result, err := info.Post("/info", payload)
	if err != nil {
		return nil, err
	}
	
	// Convert result to Meta struct
	if metaMap, ok := result.(map[string]interface{}); ok {
		meta := &utils.Meta{}
		if universe, ok := metaMap["universe"].([]interface{}); ok {
			for _, item := range universe {
				if assetMap, ok := item.(map[string]interface{}); ok {
					asset := utils.AssetInfo{}
					if name, ok := assetMap["name"].(string); ok {
						asset.Name = name
					}
					if szDecimals, ok := assetMap["szDecimals"].(float64); ok {
						asset.SzDecimals = int(szDecimals)
					}
					meta.Universe = append(meta.Universe, asset)
				}
			}
		}
		return meta, nil
	}
	
	return nil, fmt.Errorf("unexpected response format")
}

// SpotMeta retrieves exchange spot metadata
func (info *Info) SpotMeta() (*utils.SpotMeta, error) {
	payload := map[string]interface{}{
		"type": "spotMeta",
	}
	
	result, err := info.Post("/info", payload)
	if err != nil {
		return nil, err
	}
	
	// Convert result to SpotMeta struct
	if metaMap, ok := result.(map[string]interface{}); ok {
		spotMeta := &utils.SpotMeta{}
		
		// Parse universe
		if universe, ok := metaMap["universe"].([]interface{}); ok {
			for _, item := range universe {
				if assetMap, ok := item.(map[string]interface{}); ok {
					asset := utils.SpotAssetInfo{}
					if name, ok := assetMap["name"].(string); ok {
						asset.Name = name
					}
					if index, ok := assetMap["index"].(float64); ok {
						asset.Index = int(index)
					}
					if isCanonical, ok := assetMap["isCanonical"].(bool); ok {
						asset.IsCanonical = isCanonical
					}
					if tokens, ok := assetMap["tokens"].([]interface{}); ok {
						for _, token := range tokens {
							if tokenInt, ok := token.(float64); ok {
								asset.Tokens = append(asset.Tokens, int(tokenInt))
							}
						}
					}
					spotMeta.Universe = append(spotMeta.Universe, asset)
				}
			}
		}
		
		// Parse tokens
		if tokens, ok := metaMap["tokens"].([]interface{}); ok {
			for _, item := range tokens {
				if tokenMap, ok := item.(map[string]interface{}); ok {
					token := utils.SpotTokenInfo{}
					if name, ok := tokenMap["name"].(string); ok {
						token.Name = name
					}
					if szDecimals, ok := tokenMap["szDecimals"].(float64); ok {
						token.SzDecimals = int(szDecimals)
					}
					if weiDecimals, ok := tokenMap["weiDecimals"].(float64); ok {
						token.WeiDecimals = int(weiDecimals)
					}
					if index, ok := tokenMap["index"].(float64); ok {
						token.Index = int(index)
					}
					if tokenID, ok := tokenMap["tokenId"].(string); ok {
						token.TokenID = tokenID
					}
					if isCanonical, ok := tokenMap["isCanonical"].(bool); ok {
						token.IsCanonical = isCanonical
					}
					if evmContract, ok := tokenMap["evmContract"].(string); ok {
						token.EvmContract = &evmContract
					}
					if fullName, ok := tokenMap["fullName"].(string); ok {
						token.FullName = &fullName
					}
					spotMeta.Tokens = append(spotMeta.Tokens, token)
				}
			}
		}
		
		return spotMeta, nil
	}
	
	return nil, fmt.Errorf("unexpected response format")
}
