package hyperliquid

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/sirupsen/logrus"
	"github.com/yudaprama/hyperliquid-go-sdk/utils"
)

// API represents the main API client
type API struct {
	baseURL string
	client  *http.Client
	logger  *logrus.Logger
}

// NewAPI creates a new API instance
func NewAPI(baseURL string) *API {
	if baseURL == "" {
		baseURL = utils.MainnetAPIURL
	}

	return &API{
		baseURL: baseURL,
		client:  &http.Client{},
		logger:  logrus.New(),
	}
}

// Post makes a POST request to the specified URL path with the given payload
func (api *API) Post(urlPath string, payload utils.Any) (utils.Any, error) {
	if payload == nil {
		payload = map[string]interface{}{}
	}

	url := api.baseURL + urlPath

	// Marshal payload to JSON
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Make request
	resp, err := api.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Handle exceptions
	if err := api.handleException(resp); err != nil {
		return nil, err
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Try to parse JSON
	var result interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		// If JSON parsing fails, return error message
		return map[string]interface{}{
			"error": fmt.Sprintf("Could not parse JSON: %s", string(body)),
		}, nil
	}

	return result, nil
}

// handleException handles HTTP response errors
func (api *API) handleException(resp *http.Response) error {
	statusCode := resp.StatusCode
	if statusCode < 400 {
		return nil
	}

	// Read response body for error details
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read error response body: %w", err)
	}

	if statusCode >= 400 && statusCode < 500 {
		// Try to parse error JSON
		var errorResponse map[string]interface{}
		if err := json.Unmarshal(body, &errorResponse); err != nil {
			// If JSON parsing fails, create ClientError with raw text
			return utils.NewClientError(statusCode, nil, string(body), resp.Header, nil)
		}

		if errorResponse == nil {
			return utils.NewClientError(statusCode, nil, string(body), resp.Header, nil)
		}

		// Extract error details
		var errorCode *string
		var errorMessage string
		var errorData interface{}

		if code, ok := errorResponse["code"].(string); ok {
			errorCode = &code
		}

		if msg, ok := errorResponse["msg"].(string); ok {
			errorMessage = msg
		} else {
			errorMessage = string(body)
		}

		if data, ok := errorResponse["data"]; ok {
			errorData = data
		}

		return utils.NewClientError(statusCode, errorCode, errorMessage, resp.Header, errorData)
	}

	// Server error (5xx)
	return utils.NewServerError(statusCode, string(body))
}
