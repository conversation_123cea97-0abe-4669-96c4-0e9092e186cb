package main

import (
	"fmt"
	"os"

	"github.com/yudaprama/hyperliquid-go-sdk"
	"github.com/yudaprama/hyperliquid-go-sdk/utils"
)

func main() {
	/*
		Sets up an environment for testing purposes by creating an agent that can place trades on behalf of the account.
		The agent does not have permission to transfer or withdraw funds. You can run this part on a separate machine or
		change the code to connect the agent via a wallet app instead of using your private key directly in Go.
		You can also create a named agent using the frontend, which persists the authorization under an agent name.

		The main function then proceeds to place a test order with the agent and simulates the process of managing orders
		and ensuring that orders that are no longer needed are cleaned up.
		Finally, it creates an extra agent that persists beyond the current session and places an order with the extra agent.
	*/

	// Set up the environment (exchange, account info, etc.) for testing purposes.
	testnetURL := utils.TestnetAPIURL
	address, info, exchange, err := Setup(&testnetURL, true, nil)
	if err != nil {
		fmt.Printf("Setup failed: %v\n", err)
		os.Exit(1)
	}

	// Ensure that the wallet address and account address are the same.
	// If these are not the same then an agent will be approved for the wallet address instead of the account address, and the order will fail.
	if address != exchange.Wallet.Address {
		fmt.Println("You should not create an agent using an agent")
		os.Exit(1)
	}

	approveResult, agentKey, err := exchange.ApproveAgent(nil)
	if err != nil {
		fmt.Printf("Error approving agent: %v\n", err)
		os.Exit(1)
	}

	// Check if the agent approval was successful. If not, log the error and return.
	// This prevents proceeding with an agent that isn't properly authorized.
	approveResultMap, ok := approveResult.(map[string]any)
	if !ok {
		fmt.Println("Invalid approve result format")
		os.Exit(1)
	}

	status, ok := approveResultMap["status"].(string)
	if !ok || status != "ok" {
		fmt.Printf("approving agent failed: %+v\n", approveResult)
		os.Exit(1)
	}

	// Create the agent's local account using the agent's private key.
	// We use NewLocalAccount to securely generate the agent's account from its private key.
	agentAccount := utils.NewLocalAccount("", agentKey)
	fmt.Printf("Running with agent address: %s\n", agentAccount.Address)

	// Create a new exchange instance for the agent, providing it with the agent's account information and exchange URL.
	// This exchange object will be used for placing orders and interacting with the Hyperliquid API.
	agentExchange := hyperliquid.NewExchange(agentAccount, &testnetURL, nil, nil, &address, nil, nil)

	// Place a test order with the agent (setting a very low price so that it rests in the order book).
	// The order is placed as a "limit" order with the time-in-force set to "Good till Cancelled" (GTC).
	// This allows us to test placing an order without immediately executing it.
	orderType := utils.OrderType{
		Limit: &utils.LimitOrderType{
			Tif: utils.TifGtc,
		},
	}

	orderResult, err := agentExchange.Order("ETH", true, 0.2, 1000, orderType, false, nil, nil)
	if err != nil {
		fmt.Printf("Error placing order: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("Order result: %+v\n", orderResult)

	// If the order was placed successfully and the status is "resting," we attempt to cancel it.
	// This simulates the process of managing orders and ensuring that orders are no longer needed are cleaned up.
	orderResultMap, ok := orderResult.(map[string]any)
	if !ok {
		fmt.Println("Invalid order result format")
		os.Exit(1)
	}

	orderStatus, ok := orderResultMap["status"].(string)
	if ok && orderStatus == "ok" {
		response, ok := orderResultMap["response"].(map[string]any)
		if ok {
			data, ok := response["data"].(map[string]any)
			if ok {
				statuses, ok := data["statuses"].([]any)
				if ok && len(statuses) > 0 {
					status, ok := statuses[0].(map[string]any)
					if ok {
						if resting, exists := status["resting"]; exists {
							restingMap, ok := resting.(map[string]any)
							if ok {
								if oid, ok := restingMap["oid"].(float64); ok {
									cancelResult, err := agentExchange.Cancel("ETH", int(oid))
									if err != nil {
										fmt.Printf("Error canceling order: %v\n", err)
									} else {
										fmt.Printf("Cancel result: %+v\n", cancelResult)
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// Create an extra agent that persists beyond the current session.
	// The "persist" argument ensures that the agent remains available for future interactions and doesn't require re-approval each time.
	persistName := "persist"
	approveResult, extraAgentKey, err := exchange.ApproveAgent(&persistName)
	if err != nil {
		fmt.Printf("Error approving extra agent: %v\n", err)
		os.Exit(1)
	}

	// Check if the extra agent was successfully approved.
	approveResultMap, ok = approveResult.(map[string]any)
	if !ok {
		fmt.Println("Invalid approve result format for extra agent")
		os.Exit(1)
	}

	status, ok = approveResultMap["status"].(string)
	if !ok || status != "ok" {
		fmt.Printf("approving extra agent failed: %+v\n", approveResult)
		os.Exit(1)
	}

	// Create the extra agent account using its private key and the same process as above.
	extraAgentAccount := utils.NewLocalAccount("", extraAgentKey)
	extraAgentExchange := hyperliquid.NewExchange(extraAgentAccount, &testnetURL, nil, nil, &address, nil, nil)
	fmt.Printf("Running with extra agent address: %s\n", extraAgentAccount.Address)

	// Place an order with the extra agent using the same process as the original agent.
	fmt.Println("Placing order with extra agent")
	orderResult, err = extraAgentExchange.Order("ETH", true, 0.2, 1000, orderType, false, nil, nil)
	if err != nil {
		fmt.Printf("Error placing order with extra agent: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("Extra agent order result: %+v\n", orderResult)

	// If the extra agent's order is placed successfully, attempt to cancel it.
	orderResultMap, ok = orderResult.(map[string]any)
	if !ok {
		fmt.Println("Invalid order result format for extra agent")
		os.Exit(1)
	}

	orderStatus, ok = orderResultMap["status"].(string)
	if ok && orderStatus == "ok" {
		response, ok := orderResultMap["response"].(map[string]any)
		if ok {
			data, ok := response["data"].(map[string]any)
			if ok {
				statuses, ok := data["statuses"].([]any)
				if ok && len(statuses) > 0 {
					status, ok := statuses[0].(map[string]any)
					if ok {
						if resting, exists := status["resting"]; exists {
							restingMap, ok := resting.(map[string]any)
							if ok {
								if oid, ok := restingMap["oid"].(float64); ok {
									fmt.Println("Canceling order with extra agent")
									cancelResult, err := extraAgentExchange.Cancel("ETH", int(oid))
									if err != nil {
										fmt.Printf("Error canceling order with extra agent: %v\n", err)
									} else {
										fmt.Printf("Extra agent cancel result: %+v\n", cancelResult)
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// Clean up
	_ = info // Suppress unused variable warning
}
