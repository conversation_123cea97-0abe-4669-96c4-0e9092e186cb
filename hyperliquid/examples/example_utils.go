package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"

	"github.com/yudaprama/hyperliquid-go-sdk"
	"github.com/yudaprama/hyperliquid-go-sdk/utils"
)

// Config represents the configuration structure
type Config struct {
	SecretKey      string   `json:"secret_key"`
	AccountAddress string   `json:"account_address"`
	MultiSig       MultiSig `json:"multi_sig"`
}

// MultiSig represents multi-signature configuration
type MultiSig struct {
	AuthorizedUsers []AuthorizedUser `json:"authorized_users"`
}

// AuthorizedUser represents an authorized user for multi-sig
type AuthorizedUser struct {
	Comment        string `json:"comment"`
	SecretKey      string `json:"secret_key"`
	AccountAddress string `json:"account_address"`
}

// Setup sets up the environment for testing purposes
func Setup(baseURL *string, skipWs bool, perpDexs []string) (string, *hyperliquid.Info, *hyperliquid.Exchange, error) {
	// Get the directory of the current file
	currentDir, err := os.Getwd()
	if err != nil {
		return "", nil, nil, fmt.Errorf("failed to get current directory: %w", err)
	}
	
	configPath := filepath.Join(currentDir, "config.json")
	
	// Read config file
	configData, err := os.ReadFile(configPath)
	if err != nil {
		return "", nil, nil, fmt.Errorf("failed to read config file: %w", err)
	}
	
	var config Config
	if err := json.Unmarshal(configData, &config); err != nil {
		return "", nil, nil, fmt.Errorf("failed to parse config file: %w", err)
	}
	
	// Create account from secret key
	account := utils.NewLocalAccount("", config.SecretKey) // Address will be derived from private key
	
	address := config.AccountAddress
	if address == "" {
		address = account.Address
	}
	
	fmt.Printf("Running with account address: %s\n", address)
	if address != account.Address {
		fmt.Printf("Running with agent address: %s\n", account.Address)
	}
	
	// Create Info instance
	var url string
	if baseURL != nil {
		url = *baseURL
	}
	info := hyperliquid.NewInfo(url, skipWs, nil, nil, perpDexs)
	
	// Get user state to verify account has equity
	userState, err := info.UserState(address)
	if err != nil {
		return "", nil, nil, fmt.Errorf("failed to get user state: %w", err)
	}
	
	userStateMap, ok := userState.(map[string]interface{})
	if !ok {
		return "", nil, nil, fmt.Errorf("invalid user state format")
	}
	
	marginSummary, ok := userStateMap["marginSummary"].(map[string]interface{})
	if !ok {
		return "", nil, nil, fmt.Errorf("invalid margin summary format")
	}
	
	accountValueStr, ok := marginSummary["accountValue"].(string)
	if !ok {
		return "", nil, nil, fmt.Errorf("invalid account value format")
	}
	
	accountValue, err := strconv.ParseFloat(accountValueStr, 64)
	if err != nil {
		return "", nil, nil, fmt.Errorf("failed to parse account value: %w", err)
	}
	
	// Check spot user state (simplified check)
	spotUserState, err := info.SpotUserState(address)
	if err != nil {
		// If spot user state fails, we'll just check perp account value
		spotUserState = map[string]interface{}{"balances": []interface{}{}}
	}
	
	spotUserStateMap, ok := spotUserState.(map[string]interface{})
	if !ok {
		spotUserStateMap = map[string]interface{}{"balances": []interface{}{}}
	}
	
	balances, ok := spotUserStateMap["balances"].([]interface{})
	if !ok {
		balances = []interface{}{}
	}
	
	if accountValue == 0 && len(balances) == 0 {
		urlParts := info.API.BaseURL
		errorString := fmt.Sprintf("Not running the example because the provided account has no equity.\nNo accountValue:\nIf you think this is a mistake, make sure that %s has a balance on %s.\nIf address shown is your API wallet address, update the config to specify the address of your account, not the address of the API wallet.", address, urlParts)
		return "", nil, nil, fmt.Errorf("%s", errorString)
	}
	
	// Create Exchange instance
	exchange := hyperliquid.NewExchange(account, baseURL, nil, nil, &address, nil, perpDexs)
	
	return address, info, exchange, nil
}

// SetupMultiSigWallets sets up multi-signature wallets
func SetupMultiSigWallets() ([]*utils.LocalAccount, error) {
	// Get the directory of the current file
	currentDir, err := os.Getwd()
	if err != nil {
		return nil, fmt.Errorf("failed to get current directory: %w", err)
	}
	
	configPath := filepath.Join(currentDir, "config.json")
	
	// Read config file
	configData, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}
	
	var config Config
	if err := json.Unmarshal(configData, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}
	
	var authorizedUserWallets []*utils.LocalAccount
	
	for _, walletConfig := range config.MultiSig.AuthorizedUsers {
		account := utils.NewLocalAccount("", walletConfig.SecretKey)
		address := walletConfig.AccountAddress
		
		if account.Address != address {
			return nil, fmt.Errorf("provided authorized user address %s does not match private key", address)
		}
		
		fmt.Printf("loaded authorized user for multi-sig %s\n", address)
		authorizedUserWallets = append(authorizedUserWallets, account)
	}
	
	return authorizedUserWallets, nil
}
