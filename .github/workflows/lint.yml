name: Lint
on:
  push:
    branches:
      - master
  pull_request:

concurrency:
  # Concurrency group that uses the workflow name and PR number if available
  # or commit SHA as a fallback. If a new build is triggered under that
  # concurrency group while a previous build is running it will be canceled.
  # Repeated pushes to a PR will cancel all previous builds, while multiple
  # merges to master will not cancel.
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

jobs:
  pre-commit:
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        python-version: ['3.10']
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install poetry
        run: make poetry-download

      - name: Set up cache
        uses: actions/cache@v4
        with:
          path: |
            .venv
            ~/.cache/pre-commit
          key: venv-${{ matrix.python-version }}-${{ hashFiles('poetry.lock') }}-${{ hashFiles('.pre-commit-config.yaml') }}

      - name: Install dependencies
        run: |
          poetry config virtualenvs.in-project true
          make install

      - name: Lint
        run: |
          make pre-commit
