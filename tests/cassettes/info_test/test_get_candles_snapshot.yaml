interactions:
- request:
    body: '{"type": "meta"}'
    headers:
      Accept:
        - '*/*'
      Accept-Encoding:
        - gzip, deflate
      Connection:
        - keep-alive
      Content-Length:
        - '16'
      Content-Type:
        - application/json
      User-Agent:
        - python-requests/2.31.0
    method: POST
    uri: https://api.hyperliquid.xyz/info
  response:
    body:
      string: '{"universe":[{"maxLeverage":50,"name":"BTC","szDecimals":5},{"maxLeverage":50,"name":"ETH","szDecimals":4},{"maxLeverage":50,"name":"ATOM","szDecimals":2},{"maxLeverage":50,"name":"MATIC","szDecimals":1},{"maxLeverage":50,"name":"DYDX","szDecimals":1},{"maxLeverage":50,"name":"SOL","szDecimals":2},{"maxLeverage":50,"name":"AVAX","szDecimals":2},{"maxLeverage":50,"name":"BNB","szDecimals":3},{"maxLeverage":50,"name":"APE","szDecimals":1},{"maxLeverage":50,"name":"OP","szDecimals":1},{"maxLeverage":50,"name":"LTC","szDecimals":2},{"maxLeverage":50,"name":"ARB","szDecimals":1},{"maxLeverage":50,"name":"DOGE","szDecimals":0},{"maxLeverage":50,"name":"INJ","szDecimals":1},{"maxLeverage":50,"name":"SUI","szDecimals":1},{"maxLeverage":50,"name":"kPEPE","szDecimals":0},{"maxLeverage":50,"name":"CRV","szDecimals":1},{"maxLeverage":50,"name":"LDO","szDecimals":1},{"maxLeverage":50,"name":"LINK","szDecimals":1},{"maxLeverage":50,"name":"STX","szDecimals":1},{"maxLeverage":50,"name":"RNDR","szDecimals":1},{"maxLeverage":50,"name":"CFX","szDecimals":0},{"maxLeverage":50,"name":"FTM","szDecimals":0},{"maxLeverage":50,"name":"GMX","szDecimals":2},{"maxLeverage":50,"name":"SNX","szDecimals":1},{"maxLeverage":50,"name":"XRP","szDecimals":0},{"maxLeverage":50,"name":"BCH","szDecimals":3},{"maxLeverage":50,"name":"APT","szDecimals":2}]}'
    headers:
      access-control-allow-origin:
        - '*'
      access-control-expose-headers:
        - '*'
      content-length:
        - '1339'
      content-type:
        - application/json
      date:
        - Mon, 17 Jul 2023 21:43:21 GMT
      vary:
        - origin
        - access-control-request-method
        - access-control-request-headers
    status:
      code: 200
      message: OK
- request:
    body: '{"type": "candleSnapshot", "req": {"coin": "kPEPE", "interval": "1h", "startTime":
      1684702007000, "endTime": 1684784807000}}'
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '124'
      Content-Type:
      - application/json
      User-Agent:
      - python-requests/2.31.0
    method: POST
    uri: https://api.hyperliquid.xyz/info
  response:
    body:
      string: '[{"T":1684702799999,"c":"0.001604","h":"0.001616","i":"1h","l":"0.001601","n":709,"o":"0.001601","s":"kPEPE","t":1684699200000,"v":"520665683.0"},{"T":1684706399999,"c":"0.001591","h":"0.001603","i":"1h","l":"0.001584","n":802,"o":"0.001603","s":"kPEPE","t":1684702800000,"v":"682593287.0"},{"T":1684709999999,"c":"0.001605","h":"0.001609","i":"1h","l":"0.001591","n":424,"o":"0.001591","s":"kPEPE","t":1684706400000,"v":"316205596.0"},{"T":1684713599999,"c":"0.001596","h":"0.001608","i":"1h","l":"0.001592","n":308,"o":"0.001605","s":"kPEPE","t":1684710000000,"v":"235815170.0"},{"T":1684717199999,"c":"0.00155","h":"0.001598","i":"1h","l":"0.001532","n":1913,"o":"0.001596","s":"kPEPE","t":1684713600000,"v":"1943301529.0"},{"T":1684720799999,"c":"0.001545","h":"0.001555","i":"1h","l":"0.001535","n":990,"o":"0.001549","s":"kPEPE","t":1684717200000,"v":"727931405.0"},{"T":1684724399999,"c":"0.001542","h":"0.001555","i":"1h","l":"0.001525","n":917,"o":"0.001545","s":"kPEPE","t":1684720800000,"v":"745882596.0"},{"T":1684727999999,"c":"0.001542","h":"0.001547","i":"1h","l":"0.001534","n":455,"o":"0.001544","s":"kPEPE","t":1684724400000,"v":"317711025.0"},{"T":1684731599999,"c":"0.001548","h":"0.001557","i":"1h","l":"0.001536","n":672,"o":"0.001541","s":"kPEPE","t":1684728000000,"v":"491887591.0"},{"T":1684735199999,"c":"0.001543","h":"0.00155","i":"1h","l":"0.001537","n":428,"o":"0.001548","s":"kPEPE","t":1684731600000,"v":"269811863.0"},{"T":1684738799999,"c":"0.001563","h":"0.001576","i":"1h","l":"0.001541","n":1228,"o":"0.001543","s":"kPEPE","t":1684735200000,"v":"1075874400.0"},{"T":1684742399999,"c":"0.001564","h":"0.001568","i":"1h","l":"0.001555","n":449,"o":"0.001562","s":"kPEPE","t":1684738800000,"v":"278929296.0"},{"T":1684745999999,"c":"0.001559","h":"0.001575","i":"1h","l":"0.001557","n":681,"o":"0.001565","s":"kPEPE","t":1684742400000,"v":"447636970.0"},{"T":1684749599999,"c":"0.001572","h":"0.001586","i":"1h","l":"0.001557","n":915,"o":"0.00156","s":"kPEPE","t":1684746000000,"v":"736537598.0"},{"T":1684753199999,"c":"0.001573","h":"0.001578","i":"1h","l":"0.001567","n":429,"o":"0.001574","s":"kPEPE","t":1684749600000,"v":"225647917.0"},{"T":1684756799999,"c":"0.001583","h":"0.001591","i":"1h","l":"0.001561","n":847,"o":"0.001572","s":"kPEPE","t":1684753200000,"v":"734892082.0"},{"T":1684760399999,"c":"0.001571","h":"0.001586","i":"1h","l":"0.001567","n":696,"o":"0.001582","s":"kPEPE","t":1684756800000,"v":"461986408.0"},{"T":1684763999999,"c":"0.001575","h":"0.001586","i":"1h","l":"0.001559","n":1130,"o":"0.001571","s":"kPEPE","t":1684760400000,"v":"873852989.0"},{"T":1684767599999,"c":"0.001565","h":"0.001579","i":"1h","l":"0.001551","n":617,"o":"0.001577","s":"kPEPE","t":1684764000000,"v":"468121268.0"},{"T":1684771199999,"c":"0.00153","h":"0.001567","i":"1h","l":"0.001507","n":825,"o":"0.001567","s":"kPEPE","t":1684767600000,"v":"655465232.0"},{"T":1684774799999,"c":"0.001528","h":"0.001538","i":"1h","l":"0.001514","n":683,"o":"0.00153","s":"kPEPE","t":1684771200000,"v":"424010634.0"},{"T":1684778399999,"c":"0.00153","h":"0.001534","i":"1h","l":"0.001511","n":888,"o":"0.001527","s":"kPEPE","t":1684774800000,"v":"767341903.0"},{"T":1684781999999,"c":"0.001539","h":"0.001549","i":"1h","l":"0.001528","n":592,"o":"0.001531","s":"kPEPE","t":1684778400000,"v":"521321138.0"},{"T":1684785599999,"c":"0.00154","h":"0.001548","i":"1h","l":"0.001536","n":430,"o":"0.001538","s":"kPEPE","t":1684782000000,"v":"308219489.0"}]'
    headers:
      access-control-allow-origin:
      - '*'
      access-control-expose-headers:
      - '*'
      content-length:
      - '3479'
      content-type:
      - application/json
      date:
      - Mon, 17 Jul 2023 21:43:24 GMT
      vary:
      - origin
      - access-control-request-method
      - access-control-request-headers
    status:
      code: 200
      message: OK
version: 1
